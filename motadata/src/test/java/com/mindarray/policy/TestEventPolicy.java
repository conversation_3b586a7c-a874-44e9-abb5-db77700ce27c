/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.policy;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.EventPolicy;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.notification.Notification;
import com.mindarray.notification.NotificationEngine;
import com.mindarray.runbook.RunbookEngine;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.net.Socket;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.EVENT_POLICY_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.EVENT_SOURCE_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.EventPolicy.POLICY_CLEAR_FILTERS;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(90 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestEventPolicy
{
    private static final JsonObject IDS = new JsonObject();
    private static final AtomicLong EVENT_SOURCES = new AtomicLong(0);
    private static final Logger LOGGER = new Logger(TestMetricPolicy.class, MOTADATA_POLICY, "Test Event Policy");
    private static final AtomicBoolean SOUND_NOTIFICATION_TRIGGERED = new AtomicBoolean(false);
    private static MessageConsumer<byte[]> messageConsumer;
    private static MessageConsumer<JsonObject> messageReceiver;
    private static MessageConsumer<JsonObject> consumer = null;
    private static final PolicyEngine policyEngine = new PolicyEngine(EVENT_LOG);
    private static final PolicyEngine trapPolicyEngine = new PolicyEngine(EVENT_TRAP);

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        messageReceiver = TestUtil.vertx().eventBus().localConsumer(EVENT_NOTIFICATION, message ->
        {
            var notification = message.body();

            if (!notification.isEmpty() && Notification.NotificationType.SOUND.getName().equalsIgnoreCase(notification.getString(NOTIFICATION_TYPE)))   // check notification received for triggered policy or flap changed
            {
                LOGGER.info(String.format("received event %s", notification.encode()));

                Assertions.assertNotNull(notification.getString(SEVERITY));

                SOUND_NOTIFICATION_TRIGGERED.set(true);

                messageReceiver.unregister();
            }
        });

        var items = EventPolicyConfigStore.getStore().getItems();

        var retries = new AtomicInteger();

        for (var i = 0; i < items.size(); i++)
        {
            TestAPIUtil.delete(EVENT_POLICY_API_ENDPOINT + "/" + items.getJsonObject(i).getLong(ID), testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        retries.getAndIncrement();

                        if (retries.get() == items.size())
                        {
                            var deploymentId = Bootstrap.getDeployedVerticles().get(NotificationEngine.class.getSimpleName());

                            Bootstrap.vertx().undeploy(deploymentId, result ->
                            {
                                if (result.succeeded())
                                {
                                    var deploymentId1 = Bootstrap.getDeployedVerticles().get(RunbookEngine.class.getSimpleName());

                                    Bootstrap.vertx().undeploy(deploymentId1, asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            var payload = new JsonObject().put("assigned.log.parsers", new JsonArray().add(10000000000004L)).put(EVENT_SOURCE, "127.0.0.1").put("source.groups", new JsonArray().add(10000000000019L));

                                            TestAPIUtil.post(EVENT_SOURCE_API_ENDPOINT, payload, testContext.succeeding(httpResponse ->
                                                    testContext.verify(() ->
                                                    {
                                                        EVENT_SOURCES.set(httpResponse.bodyAsJsonObject().getLong(ID));

                                                        assertEquals(SC_OK, httpResponse.statusCode());

                                                        testContext.completeNow();
                                                    })));
                                        }
                                    });
                                }
                            });
                        }
                    })));
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {

        Bootstrap.vertx().deployVerticle(new NotificationEngine(), result ->
        {

            if (result.succeeded())
            {
                Bootstrap.vertx().deployVerticle(new RunbookEngine(), asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        TestAPIUtil.put(EVENT_SOURCE_API_ENDPOINT + "/" + EVENT_SOURCES + "/unassign", new JsonObject().put("params", new JsonArray().add(10000000000004L)), testContext.succeeding(response ->
                                testContext.verify(() ->
                                {
                                    assertEquals(SC_OK, response.statusCode());

                                    if (SOUND_NOTIFICATION_TRIGGERED.get())
                                    {
                                        testContext.completeNow();
                                    }
                                    else
                                    {
                                        testContext.failNow("sound notification not received!");
                                    }
                                })));
                    }
                });
            }
        });
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testColumnMapperEntry(VertxTestContext testContext)
    {
        try (var socket = new Socket("localhost", MotadataConfigUtil.getTCPPort()))
        {
            var outputStream = socket.getOutputStream();

            outputStream.write("<85>Sep  7 16:42:04 smit-HP-EliteBook-840-G2 su: pam_unix(su:auth): authentication failure; logname= uid=1000 euid=0 tty=pts/1 ruser=smit rhost=  user=root".getBytes());

            outputStream.flush();

            TestUtil.vertx().eventBus().<JsonObject>request(EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
            {
                try
                {
                    var eventColumns = reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS);

                    var policies = EventPolicyConfigStore.getStore().getItems();

                    // Initialize the policy engine with event columns and policies
                    policyEngine.initialize(eventColumns, policies);

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateEventPolicyWithGroupBy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"Test policy 1\",\"policy.type\":\"Log\",\"policy.tags\":[\"Default\"],\"policy.scheduled\":\"no\",\"policy.context\":{\"entity.type\":\"event.source.type\",\"entities\":[],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":1,\"trigger.mode\":\"individual\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":10,\"evaluation.frequency.unit\":\"second\",\"policy.severity\":\"WARNING\",\"policy.result.by\":[\"event.source\"],\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.actions\":{\"Runbook\":{\"WARNING\":[{\"id\":10000000000015}]},\"Notification\":{\"Email\":{\"WARNING\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\"}]},\"Sound\":{\"WARNING\":{}}}},\"policy.suppress.action\":\"no\",\"policy.archived\":\"no\",\"policy.creation.time\":1685353790,\"policy.state\":\"yes\",\"_type\":\"1\"}");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("first", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateEventPolicyWithFilter(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"Test policy 2\",\"policy.type\":\"Log\",\"policy.tags\":[\"Default\"],\"policy.scheduled\":\"no\",\"policy.context\":{\"entity.type\":\"event.source.type\",\"entities\":[\"Linux\"],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":1,\"trigger.mode\":\"individual\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":10,\"evaluation.frequency.unit\":\"second\",\"policy.severity\":\"WARNING\",\"policy.result.by\":[\"event.source\"],\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"event.source\",\"operator\":\"contain\",\"value\":\"1\"}]}]}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.timer.seconds\":0,\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{\"Runbook\":{\"WARNING\":[{\"id\":10000000000015}]},\"Notification\":{\"Email\":{\"WARNING\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\"}]},\"Sound\":{\"WARNING\":{}}}},\"policy.suppress.action\":\"no\",\"policy.archived\":\"no\",\"policy.creation.time\":1685353790,\"policy.state\":\"yes\",\"_type\":\"1\"}");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("second", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateEventPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{ \"policy.name\": \"Test policy 3\", \"policy.type\": \"Log\", \"policy.tags\": [ \"Default\" ], \"policy.scheduled\": \"no\", \"policy.context\": { \"entity.type\": \"event.source.type\", \"entities\": [ \"Linux\" ], \"data.point\": \"message\", \"aggregator\": \"count\", \"operator\": \">=\", \"value\": 1, \"trigger.mode\": \"individual\", \"evaluation.window\": 5, \"evaluation.window.unit\": \"minute\", \"evaluation.frequency\": 10, \"evaluation.frequency.unit\": \"second\", \"policy.severity\": \"WARNING\", \"policy.result.by\": [], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.renotify\": \"yes\", \"policy.monitor.polling.failed.notification.timer.seconds\": 0, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": {}, \"policy.suppress.action\": \"no\", \"policy.archived\": \"no\", \"policy.creation.time\": 1685353790, \"policy.state\": \"yes\", \"_type\": \"1\" }");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("third", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateEventPolicyAggregatorAvg(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{ \"policy.name\": \"Test policy 3-avg\", \"policy.type\": \"Log\", \"policy.tags\": [ \"Default\" ], \"policy.scheduled\": \"no\", \"policy.context\": { \"entity.type\": \"event.source.type\", \"entities\": [ \"Linux\" ], \"data.point\": \"linux.syslog.priority\", \"aggregator\": \"avg\", \"operator\": \">=\", \"value\": 0, \"trigger.mode\": \"individual\", \"evaluation.window\": 5, \"evaluation.window.unit\": \"minute\", \"evaluation.frequency\": 10, \"evaluation.frequency.unit\": \"second\", \"policy.severity\": \"WARNING\", \"policy.result.by\": [], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.renotify\": \"yes\", \"policy.monitor.polling.failed.notification.timer.seconds\": 0, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": {}, \"policy.suppress.action\": \"no\", \"policy.archived\": \"no\", \"policy.creation.time\": 1685353790, \"policy.state\": \"yes\", \"_type\": \"1\" }");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("third-avg", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateEventPolicyAggregatorSum(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{ \"policy.name\": \"Test policy 3-sum\", \"policy.type\": \"Log\", \"policy.tags\": [ \"Default\" ], \"policy.scheduled\": \"no\", \"policy.context\": { \"entity.type\": \"event.source.type\", \"entities\": [ \"Linux\" ], \"data.point\": \"linux.syslog.priority\", \"aggregator\": \"sum\", \"operator\": \">=\", \"value\": 0, \"trigger.mode\": \"individual\", \"evaluation.window\": 5, \"evaluation.window.unit\": \"minute\", \"evaluation.frequency\": 10, \"evaluation.frequency.unit\": \"second\", \"policy.severity\": \"WARNING\", \"policy.result.by\": [], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.renotify\": \"yes\", \"policy.monitor.polling.failed.notification.timer.seconds\": 0, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": {}, \"policy.suppress.action\": \"no\", \"policy.archived\": \"no\", \"policy.creation.time\": 1685353790, \"policy.state\": \"yes\", \"_type\": \"1\" }");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("third-sum", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testQualifyEventPolicy(VertxTestContext testContext) throws Exception
    {
        var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"message\":\"<85> mindarray-pc1 passwd[26328]: pam_unix(passwd:chauthtok): password changed for mindarray\",\"linux.password.audit.event.type\":\"log\",\"linux.password.audit.priority\":\"85\",\"linux.password.audit.logsource\":\"mindarray-pc1\",\"linux.password.audit.program\":\"passwd\",\"linux.password.audit.pid\":\"26328\",\"linux.password.audit.facility\":\"security/authorization messages\",\"linux.password.audit.severity\":\"Notice\",\"linux.password.audit.username\":\"mindarray\",\"event.category\":\"Linux Password Audit\",\"linux.password.audit.audit.status\":\"success\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

        policyEngine.processEvent(log);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testInspectPolicy(VertxTestContext testContext)
    {
        responseConsumer();

        TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_TEST, IDS.getLong("third"));

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if ((DatastoreConstants.PluginId.POLICY_RESULT.getName() + "-" + "policy.result").equalsIgnoreCase(event.getString(PLUGIN_ID)))
            {
                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(POLICY_TRIGGER_VALUE) && event.getString(POLICY_TRIGGER_VALUE).contains(RESULT));

                messageConsumer.unregister();

                var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"message\":\"<85> mindarray-pc1 passwd[26328]: pam_unix(passwd:chauthtok): password changed for mindarray\",\"linux.syslog.priority\":59,\"linux.password.audit.priority\":\"85\",\"linux.password.audit.logsource\":\"mindarray-pc1\",\"linux.password.audit.program\":\"passwd\",\"linux.password.audit.pid\":\"26328\",\"linux.password.audit.facility\":\"security/authorization messages\",\"linux.password.audit.severity\":\"Notice\",\"linux.password.audit.username\":\"mindarray\",\"event.category\":\"Linux Password Audit\",\"linux.password.audit.audit.status\":\"success\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

                policyEngine.processEvent(log);

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testInspectPolicyAggregatorAvg(VertxTestContext testContext)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if ((DatastoreConstants.PluginId.POLICY_RESULT.getName() + "-" + "policy.result").equalsIgnoreCase(event.getString(PLUGIN_ID)))
            {
                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(POLICY_TRIGGER_VALUE) && event.getString(POLICY_TRIGGER_VALUE).contains(RESULT));

                messageConsumer.unregister();

                testContext.completeNow();
            }
        });

        var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"linux.syslog.priority\":50,\"message\":\"<85> mindarray-pc1 passwd[26328]: sample log\",\"event.category\":\"Linux Audit\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(500, timer ->
        {

            policyEngine.processEvent(log);

            retries.incrementAndGet();

            if (retries.get() == 2)
            {
                responseConsumer();
            }
            else if (retries.get() == 4)
            {
                TestUtil.vertx().cancelTimer(timer);

                TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_TEST, IDS.getLong("third"));
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testInspectPolicyAggregatorSum(VertxTestContext testContext)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if ((DatastoreConstants.PluginId.POLICY_RESULT.getName() + "-" + "policy.result").equalsIgnoreCase(event.getString(PLUGIN_ID)))
            {
                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(POLICY_TRIGGER_VALUE) && event.getString(POLICY_TRIGGER_VALUE).contains(RESULT));

                messageConsumer.unregister();

                testContext.completeNow();

            }
        });

        var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"linux.syslog.priority\":50,\"message\":\"<85> mindarray-pc1 passwd[26328]: sample log\",\"event.category\":\"Linux Audit\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(500, timer ->
        {

            TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_QUALIFY, log);

            retries.incrementAndGet();

            if (retries.get() == 2)
            {
                responseConsumer();
            }
            else if (retries.get() == 4)
            {
                TestUtil.vertx().cancelTimer(timer);

                TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_TEST, IDS.getLong("third-sum"));
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetAllEventPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.get(EVENT_POLICY_API_ENDPOINT + "?filter=" + new JsonObject().put("key", POLICY_TYPE).put("value", new JsonArray().add("Log"))
                , testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonArray(RESULT);

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetEventPolicyReferences(VertxTestContext testContext) throws Exception
    {
        TestAPIUtil.get(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("first") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonObject(RESULT).getJsonArray(APIConstants.Entity.EVENT_SOURCE.getName());

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testDisableEventPolicy(VertxTestContext testContext) throws Exception
    {
        TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("first") + "/state", new JsonObject().put(POLICY_STATE, NO), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Disabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testEnableEventPolicy(VertxTestContext testContext) throws Exception
    {
        TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("first") + "/state", new JsonObject().put(POLICY_STATE, YES), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Enabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testUpdateEventPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var update = new JsonObject().put(POLICY_NAME, "Update Test policy");

        TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("first"), update, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(EventPolicyConfigStore.getStore(), update, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testTriggerEmailAndSMSActionEventPolicy(VertxTestContext testContext)
    {
        var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"message\":\"<85> mindarray-pc1 passwd[26328]: pam_unix(passwd:chauthtok): password changed for mindarray\",\"linux.password.audit.event.type\":\"log\",\"linux.password.audit.priority\":\"85\",\"linux.password.audit.logsource\":\"mindarray-pc1\",\"linux.password.audit.program\":\"passwd\",\"linux.password.audit.pid\":\"26328\",\"linux.password.audit.facility\":\"security/authorization messages\",\"linux.password.audit.severity\":\"Notice\",\"linux.password.audit.username\":\"mindarray\",\"event.category\":\"Linux Password Audit\",\"linux.password.audit.audit.status\":\"success\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

        var retries = new AtomicInteger(0);

        // check notification received for triggered policy or flap changed
        consumer = TestUtil.vertx().eventBus().localConsumer(EVENT_NOTIFICATION, message ->
        {
            var notification = message.body();

            if (!notification.isEmpty() && Notification.NotificationType.EMAIL.getName().equalsIgnoreCase(notification.getString(NOTIFICATION_TYPE)))   // check notification received for triggered policy or flap changed
            {
                Assertions.assertTrue(notification.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"));

                consumer.unregister(result -> testContext.completeNow());
            }
        });

        TestUtil.vertx().setPeriodic(500, timer ->
        {

            policyEngine.processEvent(log);

            retries.incrementAndGet();

            if (retries.get() == 2)
            {
                responseConsumer();
            }
            else if (retries.get() == 4)
            {
                TestUtil.vertx().cancelTimer(timer);

                TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_TEST, IDS.getLong("first"));
            }

        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testTriggerRunbookActionEventPolicy(VertxTestContext testContext) throws Exception
    {
        var log = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1698636453,\"message\":\"<85> mindarray-pc1 passwd[26328]: pam_unix(passwd:chauthtok): password changed for mindarray\",\"linux.password.audit.event.type\":\"log\",\"linux.password.audit.priority\":\"85\",\"linux.password.audit.logsource\":\"mindarray-pc1\",\"linux.password.audit.program\":\"passwd\",\"linux.password.audit.pid\":\"26328\",\"linux.password.audit.facility\":\"security/authorization messages\",\"linux.password.audit.severity\":\"Notice\",\"linux.password.audit.username\":\"mindarray\",\"event.category\":\"Linux Password Audit\",\"linux.password.audit.audit.status\":\"success\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1698657168}");

        var retries = new AtomicInteger(0);

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_RUNBOOK, message ->
        {
            var event = message.body();

            Assertions.assertFalse(event.isEmpty());

            Assertions.assertTrue(event.containsKey(ID));

            Assertions.assertTrue(event.containsKey(POLICY_ID));

            Assertions.assertTrue(event.containsKey(EVENT_TIMESTAMP));

            Assertions.assertTrue(event.containsKey(User.USER_NAME));

            testContext.completeNow();

            message.reply(event);
        });

        TestUtil.vertx().setPeriodic(500, timer ->
        {

            policyEngine.processEvent(log);

            retries.incrementAndGet();

            if (retries.get() == 2)
            {
                responseConsumer();
            }
            else if (retries.get() == 4)
            {
                TestUtil.vertx().cancelTimer(timer);
                TestUtil.vertx().eventBus().send(EVENT_EVENT_POLICY_TEST, IDS.getLong("second"));
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testDisableAllPolicy(VertxTestContext testContext) throws Exception
    {
        var retries = new AtomicInteger();

        for (var item : IDS)
        {
            TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + CommonUtil.getLong(item.getValue()) + "/state", new JsonObject().put(POLICY_STATE, NO), testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        testContext.awaitCompletion(10, TimeUnit.SECONDS);

                        assertEquals(NO, EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)).getString(POLICY_STATE));

                        retries.getAndIncrement();

                        if (retries.get() == IDS.size())
                        {
                            testContext.completeNow();
                        }
                    })));
        }

        testContext.awaitCompletion(20, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testCreateScheduledEventPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"Test policy 5\",\"policy.type\":\"Log\",\"policy.tags\":[\"Default\"],\"policy.scheduled\":\"yes\",\"policy.context\":{\"entity.type\":\"event.source.type\",\"entities\":[],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":5,\"trigger.mode\":\"individual\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":10,\"evaluation.frequency.unit\":\"second\",\"policy.severity\":\"WARNING\",\"policy.result.by\":[\"event.source\"],\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.renotify\":\"yes\",\"policy.monitor.polling.failed.notification.timer.seconds\":0,\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":0,\"policy.actions\":{},\"policy.suppress.action\":\"no\",\"policy.archived\":\"no\",\"policy.creation.time\":1685353790,\"policy.state\":\"yes\",\"_type\":\"1\"}");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("four", response.bodyAsJsonObject().getLong(ID));

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testUpdateScheduledEventPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var update = new JsonObject().put(POLICY_NAME, "Update Test policy").put(EventPolicy.POLICY_SCHEDULED, YES);

        TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("four"), update, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(EventPolicyConfigStore.getStore(), update, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    policyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(IDS.getLong("four")));
                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testUpdateEventPolicyAsScheduled(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"Test policy 6\",\"policy.type\":\"Log\",\"policy.tags\":[\"Default\"],\"policy.scheduled\":\"no\",\"policy.context\":{\"entity.type\":\"event.source.type\",\"entities\":[],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":5,\"trigger.mode\":\"individual\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":10,\"evaluation.frequency.unit\":\"second\",\"policy.severity\":\"WARNING\",\"policy.result.by\":[\"event.source\"],\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.renotify\":\"yes\",\"policy.monitor.polling.failed.notification.timer.seconds\":0,\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":0,\"policy.actions\":{},\"policy.suppress.action\":\"no\",\"policy.archived\":\"no\",\"policy.creation.time\":1685353790,\"policy.state\":\"yes\",\"_type\":\"1\"}");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    var update = new JsonObject().put(POLICY_NAME, "Update Test policy-1").put(EventPolicy.POLICY_SCHEDULED, YES);

                    TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID), update, testContext.succeeding(updateResponse ->
                            testContext.verify(() ->
                            {
                                TestAPIUtil.assertUpdateEntityTestResult(EventPolicyConfigStore.getStore(), update, updateResponse.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                                testContext.completeNow();
                            })));
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testDisablePolicyAction(VertxTestContext testContext) throws Exception
    {

        TestUtil.vertx().setPeriodic(1000, timer ->
        {

            if (SchedulerConfigStore.getStore().getItem(IDS.getLong("four")) == null)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.completeNow();
            }
        });

        Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(POLICY_ID, IDS.getLong("four")).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DISABLE_POLICY_ACTION_TRIGGER.name()).put(ID, IDS.getLong("four")));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testDeleteScheduledEventPolicy(VertxTestContext testContext) throws Exception
    {

        TestAPIUtil.delete(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("four"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    testContext.awaitCompletion(10, TimeUnit.SECONDS);

                    assertEquals(YES, EventPolicyConfigStore.getStore().getItem(IDS.getLong("four")).getString(POLICY_ARCHIVED));
                    policyEngine.removePolicy(IDS.getLong("four"));
                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testCreateTrapEventPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"testPolicy\",\"policy.type\":\"Trap\",\"policy.scheduled\":\"no\",\"policy.context\":{\"entity.type\":\"event.source\",\"entities\":[\"127.0.0.1\"],\"data.point\":\"trap.type\",\"operator\":\"contain\",\"value\":\"co\",\"policy.severity\":\"CRITICAL\",\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.renotify\":\"yes\",\"policy.monitor.polling.failed.notification.timer.seconds\":0,\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":0,\"policy.actions\":{},\"policy.clear.state\":\"yes\",\"policy.suppress.action\":\"no\"}");

        var filter = new JsonObject("{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":2,\"operator\":\"contain\",\"value\":\"fail\"}]}]}}");

        var clearFilter = new JsonObject("{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":3,\"operator\":\"contain\",\"value\":\"clear\"}]}]}}");

        context.put(POLICY_CONTEXT, context.getJsonObject(POLICY_CONTEXT).put(FILTERS, filter).put(POLICY_CLEAR_FILTERS, clearFilter));

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            trapPolicyEngine.addPolicy(EventPolicyConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(ID)));

                            testContext.completeNow();
                        }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    @Timeout(100 * 1000)
    void testInspectTrapPolicy(VertxTestContext testContext) throws Exception
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if ((DatastoreConstants.PluginId.POLICY_RESULT.getName() + "-" + "policy.result").equalsIgnoreCase(event.getString(PLUGIN_ID)))
            {
                LOGGER.info("received event " + event.encode());

                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(POLICY_TRIGGER_VALUE) && event.getString(POLICY_TRIGGER_VALUE).contains(RESULT) && event.getString(POLICY_TRIGGER_VALUE).contains("trap.type^LAST"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.message"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.type"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.oid"));

                Assertions.assertTrue(Severity.CRITICAL.name().equalsIgnoreCase(event.getString(SEVERITY)));

                messageConsumer.unregister();

                testContext.completeNow();
            }
        });

        var trap = new JsonObject("{\"trap.enterprise.id\":\"0.0\",\"trap.oid\":\".1.3.6.1.6.3.1.1.5.1\",\"trap.generic.name\":\"Cold Start\",\"trap.version\":\"v1\",\"event.timestamp\":1701411768,\"event.source\":\"127.0.0.1\",\"trap.severity\":\"unknown\",\"trap.type\":\"coldStart\",\"trap.message\":\"A coldStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself and that its configuration may have been altered.\",\"trap.raw.message\":{\".1.1.1.1.1\":\"filter\",\".2.2.2.2.2\":\"fail\"},\"trap.variables\":[\"filter\",\"fail\"],\"trap.vendor\":\"NXNETWORK\",\"plugin.id\":500001}");

        trapPolicyEngine.processEvent(trap.put(EVENT, EVENT_TRAP));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    @Timeout(100 * 1000)
    void testClearTrapPolicy(VertxTestContext testContext) throws Exception
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if ((DatastoreConstants.PluginId.POLICY_RESULT.getName() + "-" + "policy.result").equalsIgnoreCase(event.getString(PLUGIN_ID)))
            {
                LOGGER.info("received event " + event.encode());

                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(POLICY_TRIGGER_VALUE) && event.getString(POLICY_TRIGGER_VALUE).contains(RESULT) && event.getString(POLICY_TRIGGER_VALUE).contains("trap.type^LAST"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.message"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.type"));

                Assertions.assertTrue(event.getString(POLICY_TRIGGER_VALUE).contains("trap.oid"));

                Assertions.assertTrue(Severity.CLEAR.name().equalsIgnoreCase(event.getString(SEVERITY)));

                messageConsumer.unregister();

                testContext.completeNow();
            }
        });

        trapPolicyEngine.processEvent(new JsonObject("{\"trap.enterprise.id\":\"0.0\",\"trap.oid\":\".1.3.6.1.6.3.1.1.5.1\",\"trap.generic.name\":\"Cold Start\",\"trap.version\":\"v1\",\"event.timestamp\":1701411768,\"event.source\":\"127.0.0.1\",\"trap.severity\":\"unknown\",\"trap.type\":\"coldStart\",\"trap.message\":\"A coldStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself and that its configuration may have been altered.\",\"trap.raw.message\":{\".1.1.1.1.1\":\"filter\",\".2.2.2.2.2\":\"fail\",\".3.3.3.3.3\":\"clear\"},\"trap.variables\":[\"filter\",\"fail\",\"clear\"],\"trap.vendor\":\"NXNETWORK\",\"plugin.id\":500001}").put(EVENT, EVENT_TRAP));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testEvaluatePolicy(VertxTestContext testContext)
    {

        Assertions.assertTrue(evaluateCondition(true, Operator.EQUAL.getName(), 123, 123));
        Assertions.assertFalse(evaluateCondition(true, Operator.EQUAL.getName(), "critical", "warning"));

        Assertions.assertTrue(evaluateCondition(true, Operator.NOT_EQUAL.getName(), 123, 1234));
        Assertions.assertFalse(evaluateCondition(true, Operator.NOT_EQUAL.getName(), "critical", "critical"));

        Assertions.assertTrue(evaluateCondition(true, Operator.GREATER_THAN.getName(), 123, 1235));

        Assertions.assertTrue(evaluateCondition(true, Operator.LESS_THAN.getName(), 123, 12));

        Assertions.assertTrue(evaluateCondition(true, Operator.GREATER_THAN_OR_EQUAL.getName(), 123, 123));

        Assertions.assertTrue(evaluateCondition(true, Operator.LESS_THAN_OR_EQUAL.getName(), 1234, 123));

        Assertions.assertTrue(evaluateCondition(true, Operator.ABOVE_OR_BELOW.getName(), "123or30", 1234));

        Assertions.assertTrue(evaluateCondition(true, Operator.CONTAINS.getName(), "critical", "critical-major"));

        Assertions.assertTrue(evaluateCondition(true, Operator.NOT_CONTAINS.getName(), "critical", "major"));

        Assertions.assertTrue(evaluateCondition(true, Operator.IN.getName(), new JsonArray().add("23").add("34").add("34").add("54"), 34));

        Assertions.assertTrue(evaluateCondition(true, Operator.RANGE.getName(), new JsonArray().add("0").add("10"), 8));

        Assertions.assertFalse(evaluateCondition(true, Operator.RANGE.getName(), new JsonArray().add("0").add("10"), 13));

        Assertions.assertTrue(evaluateCondition(true, Operator.NOT_IN.getName(), new JsonArray().add("23").add("34").add("34").add("54"), 55));

        Assertions.assertTrue(evaluateCondition(true, Operator.START_WITH.getName(), "dev", "development"));

        Assertions.assertTrue(evaluateCondition(true, Operator.END_WITH.getName(), "ter", "starter"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testCreateScheduledPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"test-scheduled-policy\",\"policy.type\":\"Log\",\"policy.scheduled\":\"yes\",\"scheduler.job.type\":\"Event Policy\",\"scheduler.start.date\":\"27-01-2025\",\"scheduler.times\":[\"00:00\"],\"scheduler.timeline\":\"Once\",\"policy.context\":{\"entities\":[],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":0,\"trigger.mode\":\"combined\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":1,\"evaluation.frequency.unit\":\"minute\",\"policy.severity\":\"MAJOR\",\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.title\":\"$$$severity$$$ - $$$policy.type$$$ : $$$policy.name$$$ has been triggered\",\"policy.message\":\"$$$policy.type$$$: $$$policy.name$$$ has been triggered with $$$severity$$$ severity\",\"policy.actions\":{\"Notification\":{\"Email\":{},\"channels\":{}},\"Integration\":{}},\"policy.renotify\":\"no\",\"policy.suppress.action\":\"no\",\"policy.clear.state\":\"no\"}");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(EventPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("scheduled", response.bodyAsJsonObject().getLong(ID));

                    var retries = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(3 * 1000, timer ->
                    {
                        if (retries.get() == 3)
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("max attempt reached.");
                        }
                        else
                        {
                            var item = SchedulerConfigStore.getStore().getItem(IDS.getLong("scheduled"));

                            LOGGER.info("SchedulerConfigStore: " + SchedulerConfigStore.getStore().getItems());

                            //scheduler will be created for scheduled policy, item should not be null for this policy
                            if (item != null)
                            {
                                testContext.completeNow();
                            }

                            retries.incrementAndGet();
                        }
                    });
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testUpdateScheduledPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = new JsonObject("{\"policy.name\":\"test-scheduled-policy\",\"policy.type\":\"Log\",\"policy.scheduled\":\"no\",\"policy.context\":{\"entities\":[],\"data.point\":\"message\",\"aggregator\":\"count\",\"operator\":\">=\",\"value\":0,\"trigger.mode\":\"combined\",\"evaluation.window\":5,\"evaluation.window.unit\":\"minute\",\"evaluation.frequency\":1,\"evaluation.frequency.unit\":\"minute\",\"policy.severity\":\"MAJOR\",\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{}}},\"policy.title\":\"$$$severity$$$ - $$$policy.type$$$ : $$$policy.name$$$ has been triggered\",\"policy.message\":\"$$$policy.type$$$: $$$policy.name$$$ has been triggered with $$$severity$$$ severity\",\"policy.actions\":{\"Notification\":{\"Email\":{},\"channels\":{}},\"Integration\":{}},\"policy.renotify\":\"no\",\"policy.suppress.action\":\"no\",\"policy.clear.state\":\"no\"}");

        TestAPIUtil.put(EVENT_POLICY_API_ENDPOINT + "/" + IDS.getLong("scheduled"), context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(EventPolicyConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.EVENT_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    var retries = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(3 * 1000, timer ->
                    {
                        if (retries.get() == 3)
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("max attempt reached.");
                        }
                        else
                        {
                            var item = SchedulerConfigStore.getStore().getItem(IDS.getLong("scheduled"));

                            LOGGER.info("SchedulerConfigStore: " + SchedulerConfigStore.getStore().getItems());

                            //After we Update the policy to real-time policy, scheduler will be deleted
                            if (item == null)
                            {
                                testContext.completeNow();
                            }

                            retries.incrementAndGet();
                        }
                    });
                })));
    }

    public void responseConsumer()
    {
        TestUtil.vertx().eventBus().<Long>localConsumer(EVENT_EVENT_POLICY_AGGREGATOR_QUERY + DOT_SEPARATOR + "test", message ->
        {
            try
            {
                var id = message.body();

                var item = new JsonObject().put(ID, id).put(RESULT, policyEngine.getPolicyStats(id)); //taking variable for logging purpose...

                message.reply(item);

                if(CommonUtil.debugEnabled())
                {
                    LOGGER.debug(EventPolicyConfigStore.getStore().getItem(id).getString(POLICY_NAME) + " :  " + item);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        });
    }
}
