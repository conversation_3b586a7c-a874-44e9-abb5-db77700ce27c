/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: TAG_RULE_API_ENDPOINT For Rule Based Tagging
 */

package com.mindarray;

public class TestAPIConstants
{
    public static final String GROUP_API_ENDPOINT = "/api/v1/settings/groups/";

    public static final String AUDIT_USER_API_ENDPOINT = "/api/v1/audit/users";

    public static final String PASSWORD_POLICY_API_ENDPOINT = "/api/v1/settings/password-policy";

    public static final String APPLICATION_MAPPER_API_ENDPOINT = "/api/v1/settings/application-mappers/";

    public static final String FLOW_AS_MAPPER_API_ENDPOINT = "/api/v1/settings/flow-as-mappers/";

    public static final String FLOW_DOMAIN_MAPPER_API_ENDPOINT = "/api/v1/settings/flow-domain-mappers/";

    public static final String FLOW_IP_MAPPER_API_ENDPOINT = "/api/v1/settings/flow-ip-mappers/";

    public static final String FLOW_GEOLOCATION_MAPPER_API_ENDPOINT = "/api/v1/settings/flow-geolocation-mappers/";

    public static final String INTEGRATION_API_ENDPOINT = "/api/v1/settings/integrations";

    public static final String MAC_SCANNER_API_ENDPOINT = "/api/v1/settings/mac-scanners/";

    public static final String BUSINESS_HOUR_API_ENDPOINT = "/api/v1/settings/business-hours/";

    public static final String CUSTOM_MONITORING_FIELD_API_ENDPOINT = "/api/v1/settings/custom-monitoring-fields";

    public static final String LDAP_SERVER_API_ENDPOINT = "/api/v1/settings/ldap-servers";

    public static final String INTEGRATION_PROFILE_API_ENDPOINT = "/api/v1/settings/integration-profiles";

    public static final String EMAIL_CONFIG_API_ENDPOINT = "/api/v1/settings/mail-server-configuration/";

    public static final String METRIC_PLUGIN_API_ENDPOINT = "/api/v1/settings/metric-plugins";

    public static final String RUNBOOK_PLUGIN_API_ENDPOINT = "/api/v1/settings/runbook-plugins/";

    public static final String REMOTE_EVENT_PROCESSOR_API_ENDPOINT = "/api/v1/settings/remote-event-processors";

    public static final String REBRANDING_API_ENDPOINT = "/api/v1/settings/rebranding";

    public static final String DEPENDENCY_MAPPER_API_ENDPOINT = "/api/v1/settings/dependency-mappers";

    public static final String TOPOLOGY_PLUGIN_API_ENDPOINT = "/api/v1/settings/topology-plugins";

    public static final String CREDENTIAL_PROFILE_API_ENDPOINT = "/api/v1/settings/credential-profiles";

    public static final String OBJECT_API_ENDPOINT = "/api/v1/settings/objects";

    public static final String METRIC_API_ENDPOINT = "/api/v1/settings/metrics";

    public static final String TAG_API_ENDPOINT = "/api/v1/settings/tags";

    public static final String SCHEDULER_API_ENDPOINT = "/api/v1/settings/schedulers";

    public static final String OBJECT_PROVISION_API_ENDPOINT = "/api/v1/settings/objects/provision";

    public static final String PROTOCOL_MAPPER_API_ENDPOINT = "/api/v1/settings/protocol-mappers/";

    public static final String USER_API_ENDPOINT = "/api/v1/settings/users/";

    public static final String USER_ROLE_API_ENDPOINT = "/api/v1/settings/user-roles/";

    public static final String SMS_GATEWAY_CONFIGURATION_API_ENDPOINT = "/api/v1/settings/sms-gateway-configuration/";

    public static final String SNMP_DEVICE_CATALOG_API_ENDPOINT = "/api/v1/settings/snmp-device-catalogs";

    public static final String SNMP_DEVICE_VENDOR_API_ENDPOINT = "/api/v1/settings/snmp-device-catalogs/vendors";

    public static final String MISC_COLUMN_MAPPER_API_ENDPOINT = "/api/v1/misc/column-mappers";

    public static final String MISC_GEO_DB_COUNTRIES_API_ENDPOINT = "/api/v1/misc/countries";

    public static final String MISC_INDEXABLE_API_ENDPOINT = "/api/v1/misc/indexable-columns";

    public static final String MISC_MAKE_MODEL_API_ENDPOINT = "/api/v1/misc/models";

    public static final String SNMP_OID_GROUP_API_ENDPOINT = "/api/v1/settings/snmp-oid-groups";

    public static final String SNMP_OID_GROUP_SEARCH_API_ENDPOINT = "/api/v1/settings/snmp-oid-groups-search";

    public static final String SYSTEM_FILE_API_ENDPOINT = "/api/v1/settings/system-files";

    public static final String SYSTEM_PROCESS_API_ENDPOINT = "/api/v1/settings/system-processes";

    public static final String SYSTEM_SERVICE_API_ENDPOINT = "/api/v1/settings/system-services";

    public static final String DISCOVERY_API_ENDPOINT = "/api/v1/settings/discoveries";

    public static final String APPLICATION_DISCOVERY_API_ENDPOINT = "/api/v1/settings/discoveries/application/run";

    public static final String DISCOVERY_RUN_API_ENDPOINT = "/api/v1/settings/discoveries/%s/run";

    public static final String SNMP_TRAP_PROFILE_API_ENDPOINT = "/api/v1/settings/snmp-trap-profiles";

    public static final String SNMP_TRAP_FORWARDER_API_ENDPOINT = "/api/v1/settings/snmp-trap-forwarders";

    public static final String SNMP_TRAP_LISTENER_API_ENDPOINT = "/api/v1/settings/snmp-trap-listener-configuration";

    public static final String MY_ACCOUNT_API_ENDPOINT = "/api/v1/settings/my-account/";

    public static final String AGENT_API_ENDPOINT = "/api/v1/settings/agents/";

    public static final String MOTADATA_APP_ENDPOINT = "/api/v1/system/motadata-app";

    public static final String PROXY_SERVER_API_ENDPOINT = "/api/v1/settings/proxy-server";

    public static final String TWO_FACTOR_AUTHENTICATION_API_ENDPOINT = "/api/v1/settings/two-factor-authentication";

    public static final String ACCESS_TOKEN_API_ENDPOINT = "/api/v1/token";

    public static final String OBJECT_TAG_API_ENDPOINT = "/api/v1/settings/tags/";

    public static final String MISC_FLOW_INTERFACE_API_ENDPOINT = "/api/v1/misc/flow-interfaces";

    public static final String VISUALIZATION_DASHBOARD_API_ENDPOINT = "/api/v1/visualization/dashboards";

    public static final String VISUALIZATION_TEMPLATE_API_ENDPOINT = "/api/v1/visualization/templates";

    public static final String VISUALIZATION_WIDGET_API_ENDPOINT = "/api/v1/visualization/widgets";

    public static final String VISUALIZATION_EXPLORER_API_ENDPOINT = "/api/v1/visualization/explorers";

    public static final String LICENSE_API_ENDPOINT = "/api/v1/settings/license/";

    public static final String LOG_PARSER_API_ENDPOINT = "/api/v1/settings/log-parsers";

    public static final String LOG_FORWARDER_API_ENDPOINT = "/api/v1/settings/log-forwarders";

    public static final String LOG_COLLECTOR_API_ENDPOINT = "/api/v1/settings/log-collectors";

    public static final String EVENT_SOURCE_API_ENDPOINT = "/api/v1/settings/event-sources";

    public static final String LOG_PARSER_PLUGIN_API_ENDPOINT = "/api/v1/settings/log-parser-plugins";

    public static final String FLOW_IP_GROUP_API_ENDPOINT = "/api/v1/settings/flow-ip-groups";

    public static final String FLOW_SAMPLING_RATES = "/api/v1/settings/flow-sampling-rates";

    public static final String FLOW_SETTINGS_API_ENDPOINT = "/api/v1/settings/flow-settings";

    public static final String REPORT_API_ENDPOINT = "/api/v1/visualization/reports";

    public static final String METRIC_POLICY_API_ENDPOINT = "/api/v1/settings/metric-policies";

    public static final String EVENT_POLICY_API_ENDPOINT = "/api/v1/settings/event-policies";

    public static final String TAG_RULE_API_ENDPOINT = "/api/v1/settings/tag-rules";

    public static final String DATA_RETENTION_API_ENDPOINT = "/api/v1/settings/data-retention-policy/";

    public static final String CONFIG_TEMPLATE_API_ENDPOINT = "/api/v1/settings/config-templates";
    public static final String STORAGE_PROFILE_API_ENDPOINT = "/api/v1/settings/storage-profiles";

    public static final String CONFIGURATION_API_ENDPOINT = "/api/v1/settings/configurations";

    public static final String DNS_SERVER_API_ENDPOINT = "/api/v1/settings/dns-server-profiles";

    public static final String MISC_API_ENDPOINT = "/api/v1/misc/";

    public static final String COMPLIANCE_RULE_API_ENDPOINT = "/api/v1/settings/compliance-rules/";
    public static final String COMPLIANCE_BENCHMARK_API_ENDPOINT = "/api/v1/settings/compliance-benchmarks/";
    public static final String COMPLIANCE_POLICY_API_ENDPOINT = "/api/v1/settings/compliance-policies/";

    public static final String BACKUP_PROFILE_API_ENDPOINT = "/api/v1/settings/backup-profiles";

    public static final String MOTADATA_APP_PROFILE_API_ENDPOINT = "/api/v1/system/motadata-app";

    public static final String VISUALIZATION_QUERY_API_ENDPOINT = "/api/v1/query/visualization";

    public static final String QUERY_METRIC_HISTOGRAM_API_ENDPOINT = "/api/v1/query/metric/histogram";

    public static final String QUERY_METRIC_AGGREGATIONS_API_ENDPOINT = "/api/v1/query/metric/aggregations";

    public static final String OBJECT_QUERY_API_ENDPOINT = "/api/v1/query/objects";

    public static final String PERSONAL_ACCESS_TOKEN_API_ENDPOINT = "/api/v1/settings/personal-access-tokens";

    public static final String SINGLE_SIGN_ON_CONFIGURATION_API_ENDPOINT = "/api/v1/settings/single-sign-on";
    public static final String SINGLE_SIGN_ON_LOGOUT_API_ENDPOINT = "/api/v1/sso/logout";
    public static final String SINGLE_SIGN_ON_CALLBACK_API_ENDPOINT = "/api/v1/sso/callback";

    public static final String NETROUTE_API_ENDPOINT = "/api/v1/settings/netroutes";
    public static final String NETROUTE_POLICY_API_ENDPOINT = "/api/v1/settings/netroute-policies";

    public static final String UPLOAD_ENDPOINT = "/upload";

    public static final String OAUTH_CALLBACK_ENDPOINT = "/oauth-callback";

    public static final String UPLOAD_IMAGE_ENDPOINT = "/upload-image";

    public static final String FORGOT_PASSWORD_ENDPOINT = "/forgot-password";

    public static final Integer SNMP_TRAP_V3_LISTENER_PORT = 1630;

    public static final Integer SNMP_TRAP_V1_V2_LISTENER_PORT = 1620;

    public static final String VIEW_ACCESS_TOKEN = "view-access-token";

    public static final String CREATE_ACCESS_TOKEN = "create-access-token";

    public static final String UPDATE_ACCESS_TOKEN = "update-access-token";

    public static final String DELETE_ACCESS_TOKEN = "delete-access-token";
}
