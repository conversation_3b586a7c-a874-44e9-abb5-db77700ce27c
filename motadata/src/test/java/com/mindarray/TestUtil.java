/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.integration.TeamsIntegration;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.vertx.core.Future;
import io.vertx.core.MultiMap;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.Cookie;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpVersion;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PASSWORD;
import static com.mindarray.datastore.DatastoreConstants.BATCH_FILE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CONTEXT;
import static com.mindarray.streaming.StreamingEngine.STREAMING_TYPE;
import static com.mindarray.util.Logger.LOG_DIRECTORY;

public class TestUtil
{
    private static final Logger LOGGER = new Logger(TestUtil.class, "test-util", "Test Util");
    private static final Vertx VERTX = Bootstrap.vertx();
    private static final MultiMap MULTI_MAP = HttpHeaders.headers().add(CommonUtil.getString(HttpHeaderNames.COOKIE), Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode());
    private static final String TEST_MODE = "test.mode";
    private static final String ARGUMENT = "argument";
    private static final String TEST_PARAMETERS = "test-parameters.json";
    private static String cookie;
    private static String sessionId;
    private static String accessToken;
    private static String refreshToken;
    private static WebClient webClient;

    public static String getAccessToken()
    {
        return accessToken;
    }

    public static WebClient getWebClient()
    {
        return webClient;
    }

    public static String getSessionId()
    {
        return sessionId;
    }

    public static String getRefreshToken()
    {
        return refreshToken;
    }

    public static Vertx vertx()
    {
        return VERTX;
    }

    public static MultiMap getMultiMap()
    {
        return MULTI_MAP;
    }

    public static Future<Void> startService(VertxTestContext testContext)
    {
        var promise = Promise.<Void>promise();

        try
        {
            // will require this two upgrade.json files while creating zip for the upgrade test case
            FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + "upgrade1.json"), "{\n  \"system.bootstrap.type\": \"APP\",\n  \"system.startup.mode\": \"MASTER\"\n}");

            FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + "upgrade2.json"), "{\n  \"system.bootstrap.type\": \"APP\",\n  \"system.startup.mode\": \"MASTER\"\n}");

            // test-parameters file will be used to configure pipeline to run different bootstrap types.
            var paramsFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + TEST_PARAMETERS);

            if (paramsFile.exists())
            {
                var params = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + TEST_PARAMETERS).toJsonObject());

                System.setProperty(SYSTEM_BOOTSTRAP_TYPE, params.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()));

                System.setProperty(INSTALLATION_MODE, params.getString(INSTALLATION_MODE, InstallationMode.STANDALONE.name()));

                System.setProperty(TEST_MODE, params.getString(TEST_MODE, BootstrapType.APP.name()));

                System.setProperty(ARGUMENT, params.getString(ARGUMENT, BootstrapType.APP.name()));
            }

            // SYSTEM_BOOTSTRAP_TYPE will directly reflect into motadata.json
            if (System.getProperty(SYSTEM_BOOTSTRAP_TYPE) == null)
            {
                System.setProperty(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name());
            }

            // INSTALLATION_MODE will directly reflect into motadata.json
            if (System.getProperty(INSTALLATION_MODE) == null)
            {
                System.setProperty(INSTALLATION_MODE, InstallationMode.STANDALONE.name());
            }

            // TEST_MODULE will be used to filter which test case to run
            if (System.getProperty(TEST_MODE) == null)
            {
                System.setProperty(TEST_MODE, BootstrapType.APP.name());
            }

            // ARGUMENT will be used to pass in the main method call
            if (System.getProperty(ARGUMENT) == null)
            {
                System.setProperty(ARGUMENT, BootstrapType.APP.name());
            }

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

            // load motadata.json file
            var configs = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "motadata.json").toJsonObject());

            var agentConfigs = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "agent.json").toJsonObject());

            if (file.exists())
            {
                var passoverFiles = new JsonArray().add("agent.json").add("test-parameters.json").add("motadata.json").add(TEST_PARAMETERS);

                for (var item : Objects.requireNonNull(file.listFiles()))
                {
                    if (item.isFile() && !passoverFiles.contains(item.getAbsoluteFile().getName()))
                    {
                        FileUtils.deleteQuietly(item);
                    }
                }
            }

            // delete the runtime generated files from current director
            new JsonArray().add(GlobalConstants.RUNBOOK_DIR).add("events").add("cache").add("cache-files").add("plugin-contexts").add("logs").add("config-db-backups").add("config-backup").add("config-management").add("config-db-backups-LOCAL").add("CONFIG-DB-BACKUPS-LOCAL").forEach(directory ->
            {
                var dir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + directory);

                if (dir.exists())
                {
                    FileUtils.deleteQuietly(dir);
                }
            });

            // create the required directories
            new JsonArray().add(GlobalConstants.CONFIG_DIR).add(GlobalConstants.CACHE_DIR).add(GlobalConstants.CUSTOM_PLUGIN_SCRIPT_DIR).forEach(directory ->
            {
                var dir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + directory);

                if (!dir.exists())
                {
                    dir.mkdir();
                }
            });

            // set test parameters
            configs.put(SYSTEM_BOOTSTRAP_TYPE, System.getProperty(SYSTEM_BOOTSTRAP_TYPE))
                    .put(INSTALLATION_MODE, System.getProperty(INSTALLATION_MODE))
                    .put("system.log.level", 0)
                    .put("visualization.caching", "yes")
                    .put("http.server.port", 8443)
                    .put("log.listener.udp.port", 5141)
                    .put("log.collector.udp.port", 5142)
                    .put(EVENT_SUBSCRIBER_OBSERVER_HOST, "127.0.0.1")
                    .put(EVENT_PUBLISHER_OBSERVER_HOST, "127.0.0.1")
                    .put("datastore.writer.flush.timer.seconds", 10)
                    .put("visualization.cache.refresh.timer.seconds", 10)
                    .put("log.stat.flush.timer.seconds", 10)
                    .put("manager.id", "MOTADATA")
                    .put(EVENT_SUBSCRIBER_SECONDARY_HOST, "127.0.0.1")
                    .put("aiops.policy.inspection.timer.seconds", 20)
                    .put("visualization.cache.invalidation.timer.seconds", 30)
                    .put("availability.correlation", "yes")
                    .put("deployment.type", 0)
                    .put("env.type", "test")
                    .put("event.policy.top.groups", 5)
                    .put("event.policy.groups", 10)
                    .put("log.stat.dump.timer.seconds", 10)
                    .put("flow.stat.flush.timer.seconds", 10)
                    .put("metric.poller.max.workers", 10)
                    .put("integration.ping.timer.seconds", 30)
                    .put("log.level.reset.timer.seconds", 20)
                    .put("jvm.stat.poll.timer.seconds", 60)
                    .put("flow.cache.query.timer.seconds", 5)
                    .put("user.max.forgot.password.attempts",2)
                    .put("user.forgot.password.attempts.time.window.seconds",120)
                    .put(INSTALLATION_TYPE, 1); // no need to start datastore

            agentConfigs.getJsonObject("agent").put("http.server.port", 8443);

            TestUtil.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json", Buffer.buffer(Json.encodePrettily(configs)));

            TestUtil.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json", Buffer.buffer(Json.encodePrettily(agentConfigs)));

            MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(Path.of(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json"), StandardCharsets.UTF_8)));

            System.setProperty("env.type", MotadataConfigUtil.getEnvironmentType());

            LOGGER.info("System Property \"env.type\" is " + System.getProperty("env.type"));

            LOGGER.info("System Property \"system.bootstrap.type\" is " + System.getProperty(SYSTEM_BOOTSTRAP_TYPE));

            LOGGER.info("System Property \"argument\" is " + System.getProperty("argument"));

            LOGGER.info("System Property \"installation.mode\" is " + System.getProperty(INSTALLATION_MODE));

            LOGGER.info("System Property \"test.mode\" is " + System.getProperty(TEST_MODE));

            FileUtils.writeByteArrayToFile(new File(CURRENT_DIR + PATH_SEPARATOR + "events" + PATH_SEPARATOR + "datastore-write-3" + PATH_SEPARATOR + "1710325714"), FileUtils.readFileToByteArray(new File(CURRENT_DIR + PATH_SEPARATOR + "test-plugin-metrics" + PATH_SEPARATOR + "datastore_config_file.dat")));

            FileUtils.writeByteArrayToFile(new File(CURRENT_DIR + PATH_SEPARATOR + "events" + PATH_SEPARATOR + "datastore-write-3-0" + PATH_SEPARATOR + "1710417966-1.dat"), FileUtils.readFileToByteArray(new File(CURRENT_DIR + PATH_SEPARATOR + "test-plugin-metrics" + PATH_SEPARATOR + "config_datastore_load.dat")));

            FileUtils.writeByteArrayToFile(new File(CURRENT_DIR + PATH_SEPARATOR + "events" + PATH_SEPARATOR + "datastore-write-1-primary_db_uuid_for_datastore-0" + PATH_SEPARATOR + "1710415534-1.dat"), FileUtils.readFileToByteArray(new File(CURRENT_DIR + PATH_SEPARATOR + "test-plugin-metrics" + PATH_SEPARATOR + "datastore_write_1_0_file.dat")));

            if (System.getProperty(ARGUMENT).equalsIgnoreCase(BootstrapType.MANAGER.name()))
            {
                Bootstrap.main(new String[]{});
            }
            else
            {
                Bootstrap.main(new String[]{System.getProperty("argument")});
            }

            validateService(testContext, promise);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    public static void createZipFile(String[] srcFiles, String path, String parentFolder, String newVersion)
    {
        var zipFile = new File(path);

        try
        {
            if (zipFile.exists())
            {
                FileUtils.deleteQuietly(zipFile);
            }

            var bytes = new byte[1024];

            var zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFile));

            zipOutputStream.putNextEntry(new ZipEntry(parentFolder + PATH_SEPARATOR));

            for (var file : srcFiles)
            {
                var srcFile = new File(CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + file);

                if (srcFile.exists())
                {
                    FileUtils.deleteQuietly(srcFile);
                }

                if (new File(CURRENT_DIR + PATH_SEPARATOR + file).exists())
                {
                    Vertx.vertx().fileSystem().copyBlocking(CURRENT_DIR + PATH_SEPARATOR + file, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + file);

                    var fis = new FileInputStream(srcFile);

                    if (file.equalsIgnoreCase(VERSION_FILE) || file.equalsIgnoreCase("upgrade.me"))
                    {
                        if (file.equalsIgnoreCase(VERSION_FILE))
                        {
                            VERTX.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + file, Buffer.buffer(newVersion));
                        }
                        zipOutputStream.putNextEntry(new ZipEntry(srcFile.getName()));
                    }
                    else
                    {
                        zipOutputStream.putNextEntry(new ZipEntry(parentFolder + PATH_SEPARATOR + srcFile.getName()));
                    }

                    var length = 0;

                    while ((length = fis.read(bytes)) > 0)
                    {
                        zipOutputStream.write(bytes, 0, length);
                    }

                    zipOutputStream.closeEntry();

                    fis.close();

                    FileUtils.deleteQuietly(srcFile);
                }
            }

            zipOutputStream.close();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void startEventStreaming(JsonObject filters)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_START, new JsonObject()
                .put(FILTER, filters).put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, getSessionId()).put(STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()));
    }

    public static Future<Void> generateNewToken()
    {
        var promise = Promise.<Void>promise();

        webClient.post("/api/v1/token").putHeader(HttpHeaderNames.COOKIE.toString(), cookie).sendJsonObject(new JsonObject().put(AUTH_REFRESH_TOKEN, refreshToken),
                response ->
                {
                    if (response.succeeded())
                    {
                        accessToken = "Bearer " + response.result().bodyAsJsonObject().getString(AUTH_ACCESS_TOKEN);

                        MULTI_MAP.remove(HttpHeaders.AUTHORIZATION.toString());

                        MULTI_MAP.add(HttpHeaders.AUTHORIZATION.toString(), accessToken);
                    }
                    else
                    {
                        LOGGER.fatal("Getting new token failed " + response.result().bodyAsJsonObject());
                    }

                    promise.complete();
                });

        return promise.future();

    }

    private static void validateService(VertxTestContext testContext, Promise<Void> promise)
    {
        if (Bootstrap.bootstrapType() == BootstrapType.APP)
        {
            VERTX.setPeriodic(10_000, timer ->
            {
                LOGGER.warn(Bootstrap.getDeployedVerticles());

                if (Bootstrap.getDeployedVerticles().containsKey(TeamsIntegration.class.getSimpleName()))
                {
                    VERTX.cancelTimer(timer);

                    webClient = WebClient.create(VERTX, new WebClientOptions()
                            .setProtocolVersion(HttpVersion.HTTP_2)
                            .setSsl(true)
                            .setUseAlpn(true)
                            .setTrustAll(true)
                            .setDefaultPort(MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()))
                            .setDefaultHost("localhost"));

                    cookie = Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode();

                    webClient.post("/api/v1/token").putHeader(HttpHeaderNames.COOKIE.toString(), cookie).sendJsonObject(new JsonObject().put(USER_NAME, "admin").put(USER_PASSWORD, "admin"),
                            tokenResponse ->
                            {
                                if (tokenResponse.succeeded())
                                {
                                    accessToken = "Bearer " + tokenResponse.result().bodyAsJsonObject().getString(AUTH_ACCESS_TOKEN);

                                    sessionId = tokenResponse.result().bodyAsJsonObject().getString(SESSION_ID);

                                    MULTI_MAP.add(HttpHeaders.AUTHORIZATION.toString(), accessToken);

                                    refreshToken = tokenResponse.result().bodyAsJsonObject().getString(AUTH_REFRESH_TOKEN);

                                    try
                                    {
                                        EventBusConstants.setRemoteEventProcessorRegistrationId();

                                        EventBusConstants.register(new CipherUtil());

                                        TestUtil.startEventStreaming(new JsonObject());

                                        TestNMSUtil.assertMetricPollResponseTestResult();

                                        promise.complete();
                                    }
                                    catch (Exception exception)
                                    {
                                        promise.fail(exception);

                                        testContext.failNow(exception);
                                    }
                                }
                                else
                                {
                                    promise.fail(tokenResponse.cause());

                                    testContext.failNow(tokenResponse.cause());
                                }
                            });
                }
            });
        }
        else
        {
            promise.complete();
        }
    }

    public static JsonObject decodeEvent(JsonObject event)
    {
        var buffer = Buffer.buffer(event.getBinary(EVENT_CONTEXT));

        return new JsonObject(new String(buffer.getBytes(12, 12 + buffer.getIntLE(8))));
    }

    public static JsonObject decodeMetricEnricherBuffer(JsonObject event)
    {
        var result = new JsonObject();

        try
        {
            var snappyBuffer = Buffer.buffer(CodecUtil.toBytes(event.getBinary(EVENT_CONTEXT)));

            try
            {

                if (snappyBuffer.length() > 0)
                {
                    var position = 0;

                    result.put(EventBusConstants.EVENT_TIMESTAMP, snappyBuffer.getLongLE(position));

                    position = position + 8;

                    var pluginLength = snappyBuffer.getIntLE(position);

                    position = position + 4;

                    result.put(GlobalConstants.PLUGIN_ID, snappyBuffer.getString(position, position + pluginLength, StandardCharsets.UTF_8.toString()));

                    position += pluginLength;

                    result.put(DatastoreConstants.DATASTORE_FORMAT, CommonUtil.getString(snappyBuffer.getByte(position)));

                    position += 1;

                    result.put(DatastoreConstants.DATASTORE_TYPE, CommonUtil.getString(snappyBuffer.getByte(position)));

                    position += 1;

                    result.put(AIOpsObject.OBJECT_ID, snappyBuffer.getIntLE(position));

                    position += 4;

                    var instanceLength = snappyBuffer.getIntLE(position);

                    position += 4;

                    try
                    {
                        result.put(GlobalConstants.INSTANCE, snappyBuffer.getString(position, position + instanceLength, StandardCharsets.UTF_8.toString()));

                        position += instanceLength;
                    }
                    catch (Exception e)
                    {
                        LOGGER.warn(String.format("invalid instance length decode in db metric store file %s", event.getString(BATCH_FILE)));
                    }

                    while (position < snappyBuffer.length())
                    {
                        try
                        {
                            var category = snappyBuffer.getByte(position);

                            position += 1;

                            var fieldLength = snappyBuffer.getIntLE(position);

                            position += 4;

                            var field = snappyBuffer.getString(position, position + fieldLength, StandardCharsets.UTF_8.toString());

                            position += fieldLength;

                            if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
                            {
                                var value = snappyBuffer.getLongLE(position);

                                result.put(field, value);

                                position += 8;
                            }
                            else if (category == DatastoreConstants.DataCategory.FLOAT.getName())
                            {
                                var value = ByteUtil.readDouble(snappyBuffer, position);

                                result.put(field, value);

                                position += 8;
                            }
                            else
                            {
                                try
                                {
                                    fieldLength = snappyBuffer.getIntLE(position);

                                    position += 4;

                                    var value = snappyBuffer.getString(position, position + fieldLength, StandardCharsets.UTF_8.toString());

                                    result.put(field, value);

                                    position += fieldLength;
                                }
                                catch (Exception exception)
                                {
                                    result.put(field, "dummy");

                                    LOGGER.warn(result.encode());
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.warn(result.encode());

                            LOGGER.error(exception);
                        }
                    }

                    LOGGER.warn(String.format("decoded data from bytes of metric store file %s = %s", event.getString(BATCH_FILE), result.encode()));
                }
                else
                {
                    LOGGER.warn(String.format("invalid length decode in db metric store file %s ", event.getString(BATCH_FILE)));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + LOG_DIRECTORY + System.currentTimeMillis(), Buffer.buffer(event.getBinary(EVENT_CONTEXT)));
        }

        return result;

    }

    public static JsonObject decodeEventBufferSingleRow(Buffer snappyBuffer)
    {
        var result = new JsonObject();

        var position = 0;

        result.put(EventBusConstants.EVENT_TIMESTAMP, snappyBuffer.getLongLE(position));

        position = position + 8;

        var pluginLength = snappyBuffer.getIntLE(position);

        position = position + 4;

        result.put(GlobalConstants.PLUGIN_ID, snappyBuffer.getString(position, position + pluginLength, StandardCharsets.UTF_8.toString()));

        position += pluginLength;

        result.put(DatastoreConstants.DATASTORE_FORMAT, CommonUtil.getString(snappyBuffer.getByte(position)));

        position += 1;

        result.put(DatastoreConstants.DATASTORE_TYPE, CommonUtil.getString(snappyBuffer.getByte(position)));

        position += 1;

        var sourceLength = snappyBuffer.getShortLE(position);

        position += 2;

        result.put("source", snappyBuffer.getString(position, position + sourceLength, StandardCharsets.UTF_8.toString()));

        position += sourceLength;

        while (position < snappyBuffer.length())
        {
            try
            {
                var category = snappyBuffer.getByte(position);

                position += 1;

                var fieldLength = snappyBuffer.getIntLE(position);

                position += 4;

                var field = snappyBuffer.getString(position, position + fieldLength, StandardCharsets.UTF_8.toString());

                position += fieldLength;

                if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
                {
                    var value = snappyBuffer.getLongLE(position);

                    result.put(field, value);

                    position += 8;
                }
                else if (category == DatastoreConstants.DataCategory.FLOAT.getName())
                {
                    var value = ByteUtil.readDouble(snappyBuffer, position);

                    result.put(field, value);

                    position += 8;
                }
                else
                {
                    fieldLength = snappyBuffer.getIntLE(position);

                    position += 4;

                    var value = snappyBuffer.getString(position, position + fieldLength, StandardCharsets.UTF_8.toString());

                    result.put(field, value);

                    position += fieldLength;
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        return result;
    }

}
