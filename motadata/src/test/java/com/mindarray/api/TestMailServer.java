/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.MailServerConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.EMAIL_CONFIG_API_ENDPOINT;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.MailServerConfiguration.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(62 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMailServer
{
    public static final JsonObject OFFICE365_CONTEXT_SSL = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.office365.com")
            .put(MAIL_SERVER_PORT, 465)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_SSL)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "r80aqREnDId1OJ1/pl4Vxv4tndrmHLR9L6oRPbdMWtc=")
            .put(MAIL_SERVER_PASSWORD, "oqtu3l3k8Hx0MUy9yLOL/w==");

    private static final JsonObject REQUEST_CONTEXT = new JsonObject();

    private static final JsonObject TLS_CONTEXT = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.gmail.com")
            .put(MAIL_SERVER_PORT, 587)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "Mind@123");


    private static final JsonObject SSL_CONTEXT = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.gmail.com")
            .put(MAIL_SERVER_PORT, 465)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_SSL)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "Mind@123");

    private static final JsonObject CONTEXT = new JsonObject()
            .put(MAIL_SERVER_HOST, "************")
            .put(MAIL_SERVER_PORT, 25)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_AUTH_STATUS, NO)
            .put(MAIL_SERVER_SENDER, "<EMAIL>")
            .put(MAIL_SERVER_PROTOCOL, "None");

    private static final JsonObject OFFICE365_CONTEXT_TLS = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.office365.com")
            .put(MAIL_SERVER_PORT, 587)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "Mind#@45Date!12");

    private static final JsonObject OFFICE365_CONTEXT_OAUTH = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.office365.com")
            .put(MAIL_SERVER_PORT, 587)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "dummy_access_token");

    private static final JsonObject OFFICE365_CONTEXT_NONE = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.office365.com")
            .put(MAIL_SERVER_PORT, 25)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_PROTOCOL, "None")
            .put(MAIL_SERVER_SENDER, "r80aqREnDId1OJ1/pl4Vxv4tndrmHLR9L6oRPbdMWtc=")
            .put(MAIL_SERVER_AUTH_STATUS, NO);

    private static final Logger LOGGER = new Logger(TestMailServer.class, "Mail Server", "Test Mail Server");
    private static MessageConsumer<JsonObject> messageConsumer;

    @AfterAll
    static void cleanUp(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    public static void assertMailServerTestResult(VertxTestContext testContext, String value, String eventState, String errorCode, String uuid)
    {
        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equals(UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST) &&
                            !eventContext.isEmpty() && eventContext.containsKey(UI_EVENT_UUID) && eventContext.getString(UI_EVENT_UUID).equalsIgnoreCase(uuid)
                            && eventContext.getString(STATUS) != null && eventContext.getString(MESSAGE) != null && message.body().containsKey(EVENT_TYPE))
                    {
                        LOGGER.info("received event : " + eventContext.encode());

                        testContext.verify(() ->
                        {
                            assertEquals(eventState, eventContext.getString(STATUS));

                            assertEquals(errorCode, eventContext.getString(ERROR_CODE));

                            Assertions.assertTrue(eventContext.getString(MESSAGE).contains(value));

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case: " + testInfo.getTestMethod().get().getName());

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetMailServerConfiguration(VertxTestContext testContext)
    {

        TestAPIUtil.get(EMAIL_CONFIG_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, MailServerConfigStore.getStore(), new JsonArray().add(MAIL_SERVER_USERNAME));

                    REQUEST_CONTEXT.put("mail.server", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateMailServerNoSecurity(VertxTestContext testContext, TestInfo testInfo)
    {
        var mailServerContext = new JsonObject().mergeIn(CONTEXT);

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateMailServerSSL(VertxTestContext testContext, TestInfo testInfo)
    {
        var mailServerContext = new JsonObject().mergeIn(SSL_CONTEXT);

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            // we're storing this into credential profile
                            mailServerContext.remove(MAIL_SERVER_USERNAME);

                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testMailServerNoSecurity(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(CONTEXT).put(UI_EVENT_UUID, uuid).put("email.recipient", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));


    }

    @Disabled("Gmail mail not working")
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testMailServerSSL(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(SSL_CONTEXT).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>")));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testMailServerSSLInvalidUser(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "AUTH LOGIN failed"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(SSL_CONTEXT).put(UI_EVENT_UUID, uuid).put(MAIL_SERVER_USERNAME, "xyz.com").put("target", "<EMAIL>")));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testMailServerSSLInvalidPassword(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "AUTH LOGIN failed"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(SSL_CONTEXT).put(UI_EVENT_UUID, uuid).put(MAIL_SERVER_PASSWORD, "123").put("target", "<EMAIL>")));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testUpdateMailServerTLS(VertxTestContext testContext, TestInfo testInfo)
    {
        var mailServerContext = new JsonObject().mergeIn(TLS_CONTEXT);

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            // we're storing this into credential profile
                            mailServerContext.remove(MAIL_SERVER_USERNAME);

                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testMailServerSSLInvalidHost(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equals(UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST) &&
                            !eventContext.isEmpty() && eventContext.containsKey(UI_EVENT_UUID) && eventContext.getString(UI_EVENT_UUID).equalsIgnoreCase(uuid)
                            && eventContext.getString(STATUS) != null && eventContext.getString(MESSAGE) != null && message.body().containsKey(EVENT_TYPE))
                    {
                        LOGGER.info("received event : " + eventContext.encode());

                        testContext.verify(() ->
                        {
                            assertEquals(STATUS_FAIL, eventContext.getString(STATUS));

                            assertEquals(ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, eventContext.getString(ERROR_CODE));

                            Assertions.assertTrue(eventContext.getString(MESSAGE).toLowerCase().contains("connection timed out") || eventContext.getString(MESSAGE).toLowerCase().contains("no route to host"));

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(SSL_CONTEXT).put(UI_EVENT_UUID, uuid).put(MAIL_SERVER_HOST, "*************").put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId()).put(TIMEOUT, 10)));
    }

    @Disabled("Gmail mail not working")
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testMailServerTLS(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(TLS_CONTEXT).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testMailServerTLSInvalidUser(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "AUTH LOGIN failed"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(TLS_CONTEXT).put(UI_EVENT_UUID, uuid).put(MAIL_SERVER_USERNAME, "xyz.com").put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testGetMailServerConfigurationForTLSUsernameCheck(VertxTestContext testContext)
    {

        TestAPIUtil.get(EMAIL_CONFIG_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, MailServerConfigStore.getStore(), new JsonArray().add(MAIL_SERVER_USERNAME));

                    if (!response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getString(MAIL_SERVER_USERNAME).isEmpty())
                    {
                        testContext.completeNow();
                    }

                })));
    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testUpdateOffice365MailServerTLS(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var mailServerContext = new JsonObject().mergeIn(OFFICE365_CONTEXT_TLS).put(MAIL_SERVER_USERNAME, OFFICE365_CONTEXT_TLS.getString(MAIL_SERVER_USERNAME))
                .put(MAIL_SERVER_PASSWORD, OFFICE365_CONTEXT_TLS.getString(MAIL_SERVER_PASSWORD));

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            mailServerContext.remove(MAIL_SERVER_USERNAME);

                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            OFFICE365_CONTEXT_TLS.mergeIn(mailServerContext);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testMailServerTLSInvalidPassword(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "AUTH LOGIN failed"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(TLS_CONTEXT).put(UI_EVENT_UUID, uuid).put(MAIL_SERVER_PASSWORD, "123").put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));

    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testUpdateOffice365MailServerNoSecurity(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var mailServerContext = new JsonObject().mergeIn(OFFICE365_CONTEXT_NONE).put(MAIL_SERVER_SENDER, new CipherUtil().decrypt(OFFICE365_CONTEXT_NONE.getString(MAIL_SERVER_SENDER)));

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            OFFICE365_CONTEXT_NONE.mergeIn(mailServerContext);

                            testContext.completeNow();
                        })));
    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testOffice365MailServerTLS(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(OFFICE365_CONTEXT_TLS)
                        .put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));

    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testUpdateOffice365MailServerSSL(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var mailServerContext = new JsonObject().mergeIn(OFFICE365_CONTEXT_SSL).put(MAIL_SERVER_USERNAME, new CipherUtil().decrypt(OFFICE365_CONTEXT_SSL.getString(MAIL_SERVER_USERNAME)))
                .put(MAIL_SERVER_PASSWORD, new CipherUtil().decrypt(OFFICE365_CONTEXT_SSL.getString(MAIL_SERVER_PASSWORD)));

        TestAPIUtil.put(EMAIL_CONFIG_API_ENDPOINT + REQUEST_CONTEXT.getLong("mail.server"), mailServerContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            mailServerContext.remove(MAIL_SERVER_USERNAME);

                            TestAPIUtil.assertUpdateEntityTestResult(MailServerConfigStore.getStore(), mailServerContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Mail Server Configuration"), LOGGER, testInfo.getTestMethod().get().getName());

                            OFFICE365_CONTEXT_SSL.mergeIn(mailServerContext);

                            testContext.completeNow();
                        })));
    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled("Office365 not responding today so working on that")
    @Order(19)
    void testOffice365MailNoSecurity(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "socket was closed unexpected"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(OFFICE365_CONTEXT_NONE).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId())));

    }

    //#3515
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testOffice365MailServerSSL(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, "Email server test failed.", STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, new JsonObject().mergeIn(OFFICE365_CONTEXT_SSL).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId()).put(TIMEOUT, 10)));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testMailServerNoSender(VertxTestContext testContext)
    {
        var uuid = UUID.randomUUID().toString();

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        var context = new JsonObject().mergeIn(CONTEXT).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId());

        context.remove(MAIL_SERVER_SENDER);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, context));


    }

    // skipping below testcases in test environment as subscription is expired
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testMailServerOAUTH(VertxTestContext testContext)
    {

        var uuid = UUID.randomUUID().toString();

        var id = 10000000000098L;

        var item = new JsonObject("{\"credential.profile.name\": \"sample 1\",\"credential.profile.protocol\": \"HTTP/HTTPS\",\"credential.profile.context\": {\"client.id\" : \"9a163321-587c-4086-87ab-b351c80942fb\",\"authentication.url\" : \"https://login.microsoftonline.com/250881bf-05b5-4469-b927-3d6a4928abb4/oauth2/v2.0/authorize\",\"redirect.url\" : \"https://localhost/oauth-callback\",\"token.url\" : \"https://login.microsoftonline.com/250881bf-05b5-4469-b927-3d6a4928abb4/oauth2/v2.0/token\",\"scopes\" : [ \"offline_access https://outlook.office365.com/SMTP.Send\" ],\"client.secret\" : \"****************************************\",\"authentication.provider\" : \"microsoft\",\"grant.type\" : \"Authorization Code\",\"timeout\" : 60,\"authentication.type\" : \"oauth\",\"credential.profile.name\" : \"MAIL\",\"credential.profile.protocol\" : \"HTTP/HTTPS\",\"type\" : \"update\",\"guid\" : \"7fa9726f-ff47-49e1-af24-fb859f77c55f\",\"user.name\" : \"admin\",\"event.id\" : 66302166073,\"event.type\" : \"ui.action.credential.profile.test\",\"_type\" : \"1\",\"id\" : 66302165999,\"token_type\" : \"Bearer\",\"scope\" : \"https://outlook.office365.com/SMTP.Send\",\"expires_in\" : 4912,\"ext_expires_in\" : 4912,\"access_token\" : \"eyJ0eXAiOiJKV1QiLCJub25jZSI6IkE5cU1TY0VYQ0VqT2pJdUd0dlFnWnM2NlQzaTZvWjdDcFhfNlBaZlpqeGMiLCJhbGciOiJSUzI1NiIsIng1dCI6IllUY2VPNUlKeXlxUjZqekRTNWlBYnBlNDJKdyIsImtpZCI6IllUY2VPNUlKeXlxUjZqekRTNWlBYnBlNDJKdyJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MGWMKDEQhZutbu9ejgPieSN91YGAz32vWTLoF0cqouWCjzzpz1yZK9z62mwwQN4dtz469065L93UqfCJCvARn3XvKOSBg2whJ9_G71CakM8nGAmQ4E6sznPJ_1XQH1VeF9irxSRU1zOgHR_KSYWF97yUHQsaCr2wf_xwOVNk-3UwykbftxXvB-My8i5ZS9jQA5nU32CKP0kno7QEp2aXIhcLy1hE32hCJ5wg6HMwyE1hO-p9ef2sJ9wAmx8q-D_HSg9hkhtxVOGRAe5pHBGW9heDCA_owSI9vpdb7Rn7na4JWxEL65ylS-9aWf_PO3fJ_xXPn14S6Xv_ACaDoL1oQw\",\"refresh_token\" : \"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"},\"_type\": \"1\",\"id\": 10000000000098,\"count\": 0}");

        Bootstrap.configDBService().save(DBConstants.TBL_CREDENTIAL_PROFILE,
                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        CredentialProfileConfigStore.getStore().updateItem(id);
                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });

        assertMailServerTestResult(testContext, InfoMessageConstants.MAIL_SERVER_TEST_SUCCEEDED, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS, uuid);

        var context = new JsonObject().mergeIn(OFFICE365_CONTEXT_OAUTH).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId());

        context.put(MAIL_SERVER_CREDENTIAL_PROFILE, id);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, context));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testMailServerInvalidOAUTH(VertxTestContext testContext)
    {

        var uuid = UUID.randomUUID().toString();

        var id = 10000000000099L;

        var item = new JsonObject("{\"credential.profile.name\": \"New two\",\"credential.profile.protocol\": \"HTTP/HTTPS\",\"credential.profile.context\": {\"client.id\" : \"9a163321-587c-4086-87ab-b351c80942fb\",\"authentication.url\" : \"https://login.microsoftonline.com/250881bf-05b5-4469-b927-3d6a4928abb4/oauth2/v2.0/authorize\",\"redirect.url\" : \"https://localhost/oauth-callback\",\"token.url\" : \"https://login.microsoftonline.com/250881bf-05b5-4469-b927-3d6a4928abb4/oauth2/v2.0/token\",\"scopes\" : [ \"offline_access https://outlook.office365.com/SMTP.Send\" ],\"client.secret\" : \"****************************************\",\"authentication.provider\" : \"microsoft\",\"grant.type\" : \"Authorization Code\",\"timeout\" : 60,\"authentication.type\" : \"oauth\",\"credential.profile.name\" : \"MAIL\",\"credential.profile.protocol\" : \"HTTP/HTTPS\",\"type\" : \"update\",\"guid\" : \"7fa9726f-ff47-49e1-af24-fb859f77c55f\",\"user.name\" : \"admin\",\"event.id\" : 66302166073,\"event.type\" : \"ui.action.credential.profile.test\",\"_type\" : \"1\",\"id\" : 66302165999,\"token_type\" : \"Bearer\",\"scope\" : \"https://outlook.office365.com/SMTP.Send\",\"expires_in\" : 4912,\"ext_expires_in\" : 4912,\"access_token\" : \"eyJ0eXAiOiJKV1QiLCJub25jZSI6IkE5cU1TY0VYQ0VqT2pJdUd0dlFnWnM2NlQzaTZvWjdDcFhfNlBaZlpqeGMiLCJhbGciOiJSUzI1NiIsIng1dCI6IllUY2VPNUlKeXlxUjZqekRTNWlBYnBlNDJKdyIsImtpZCI6IllUY2VPNUlKeXlxUjZqekRTNWlBYnBlNDJKdyJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MGWMKDEQhZutbu9ejgPieSN91YGAz32vWTLoF0cqouWCjzzpz1yZK9z62mwwQN4dtz469065L93UqfCJCvARn3XvKOSBg2whJ9_G71CakM8nGAmQ4E6sznPJ_1XQH1VeF9irxSRU1zOgHR_KSYWF97yUHQsaCr2wf_xwOVNk-3UwykbftxXvB-My8i5ZS9jQA5nU32CKP0kno7QEp2aXIhcLy1hE32hCJ5wg6HMwyE1hO-p9ef2sJ9wAmx8q-D_HSg9hkhtxVOGRAe5pHBGW9heDCA_owSI9vpdb7Rn7na4JWxEL65ylS-9aWf_PO3fJ_xXPn14S6Xv_ACaDoL1oQw\",\"refresh_token\" : \"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"},\"_type\": \"1\",\"id\": 10000000000099,\"count\": 0}");

        Bootstrap.configDBService().save(DBConstants.TBL_CREDENTIAL_PROFILE,
                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        CredentialProfileConfigStore.getStore().updateItem(id);
                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });

        assertMailServerTestResult(testContext, String.format(ErrorMessageConstants.MAIL_SERVER_TEST_FAILED, "AUTH XOAUTH2 failed"), STATUS_FAIL, ErrorCodes.ERROR_CODE_EMAIL_CONFIG_TEST, uuid);

        var context = new JsonObject().mergeIn(OFFICE365_CONTEXT_OAUTH).put(UI_EVENT_UUID, uuid).put("target", "<EMAIL>").put(SESSION_ID, TestUtil.getSessionId());

        context.put(MAIL_SERVER_CREDENTIAL_PROFILE, id);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, context));

    }


}
