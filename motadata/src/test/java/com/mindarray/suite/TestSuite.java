/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.suite;

import org.junit.jupiter.api.ClassOrderer;
import org.junit.jupiter.api.TestClassOrder;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

@Suite
@SelectClasses({BootstrapStandaloneSuite.class, MotadataAppManagerSuite.class, LDAPServerSuite.class, HASuite.class, TrapProcessorSuite.class, MailServerSuite.class, NotificationSuite.class, UserProfileSuite.class, CredentialProfilesSuite.class, DiscoveryProfilesSuite.class, AgentSuite.class, DiscoveryRunSuite.class, PostDiscoverySuite.class, LicenseSuite.class, ProvisionObjectSuite.class, MetricSchedulerSuite.class, RediscoverySuite.class, PollerScheduleSuite.class, DNSSuite.class, ConfigSuite.class, ComplianceSuite.class, MetricPollerSuite.class, DatastoreSuite.class, HealthSuite.class, DependencySuite.class, DependencyMapperSuite.class, CorrelationEngineSuite.class, RunbookSuite.class, TagAPISuite.class, FlowAPISuite.class, ServerAPISuite.class, VisualizationSuite.class, PolicySuite.class, FlowProcessorSuite.class, LogProcessorSuite.class, EventTrackerSuite.class, EventEngineSuite.class, ReportSuite.class, IntegrationSuite.class, StreamingEngineSuite.class, PatchSuite.class, MiscellaneousSuite.class})
@TestClassOrder(ClassOrderer.class)
public class TestSuite
{

}