/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class EventPolicy extends AbstractAPI
{
    public static final String POLICY_SCHEDULED = "policy.scheduled";
    public static final String EVALUATION_WINDOW = "evaluation.window";
    public static final String EVALUATION_WINDOW_UNIT = "evaluation.window.unit";
    public static final String TRIGGER_MODE = "trigger.mode";
    public static final String TRIGGERED_VALUE = "triggered.value";
    public static final String POLICY_CLEAR_STATE = "policy.clear.state";
    public static final String POLICY_CLEAR_FILTERS = "policy.clear.filters";
    public static final String POLICY_SUPPRESS_WINDOW = "policy.suppress.window";
    public static final String POLICY_SUPPRESS_WINDOW_UNIT = "policy.suppress.window.unit";
    public static final String POLICY_SUPPRESS_ACTION = "policy.suppress.action";
    public static final String POLICY_RESULT_BY = "policy.result.by";
    private static final Logger LOGGER = new Logger(EventPolicy.class, MOTADATA_API, "Event Policy API");

    public EventPolicy()
    {
        super("event-policies", EventPolicyConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update  policy state API
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var items = EventPolicyConfigStore.getStore().getItems();

        var requestBody = routingContext.body().asJsonObject();

        var valid = true;

        // Need to check manually as we are not deleting policy just archiving so if that policy is archived so user can create policy with same archived one..
        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if ((!item.containsKey(POLICY_ARCHIVED) || item.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)) && item.getString(POLICY_TYPE).equalsIgnoreCase(requestBody.getString(POLICY_TYPE)) && item.getString(POLICY_NAME).equalsIgnoreCase(requestBody.getString(POLICY_NAME)))
            {
                valid = false;

                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME)));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME));

                break;
            }
        }

        if (valid)
        {
            promise.complete(requestBody.put(POLICY_ARCHIVED, NO).put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()).put(POLICY_STATE, YES));
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        var requestBody = routingContext.body().asJsonObject();

        if (requestBody.getString(POLICY_SCHEDULED).equalsIgnoreCase(YES))
        {
            Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, requestBody.put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.EVENT_POLICY.getName()).put(Scheduler.SCHEDULER_STATE, YES).put(ID, entity.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
            {
                if (future.succeeded())
                {
                    SchedulerConfigStore.getStore().addItem(future.result()).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(future.result())));
                }
            });
        }

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return Future.succeededFuture(routingContext.body().asJsonObject().put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()));
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        var requestBody = routingContext.body().asJsonObject();

        var scheduler = SchedulerConfigStore.getStore().getItem(entity.getLong(ID));

        if (requestBody.getString(POLICY_SCHEDULED).equalsIgnoreCase(YES))
        {
            if (scheduler != null)
            {
                Bootstrap.configDBService().update(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, entity.getLong(ID)), requestBody.put(ID, entity.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                {
                    if (future.succeeded())
                    {
                        SchedulerConfigStore.getStore().updateItem(entity.getLong(ID)).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(entity.getLong(ID))));
                    }
                });
            }

            else
            {

                Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, requestBody.put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.EVENT_POLICY.getName()).put(Scheduler.SCHEDULER_STATE, YES).put(ID, entity.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                {
                    if (future.succeeded())
                    {
                        SchedulerConfigStore.getStore().addItem(future.result()).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(future.result())));
                    }
                });
            }
        }
        else
        {
            //if policy changed to real time from schedule policy, need to delete the respected scheduler.
            if (scheduler != null)
            {
                Bootstrap.configDBService().delete(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, entity.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                {
                    if (future.succeeded())
                    {
                        SchedulerConfigStore.getStore().deleteItem(entity.getLong(ID));

                        JobScheduler.removeJob(entity.getLong(ID));
                    }
                });
            }
        }

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        var scheduler = SchedulerConfigStore.getStore().getItem(entity.getLong(ID));

        if (scheduler != null)
        {
            Bootstrap.configDBService().delete(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, entity.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
            {
                if (future.succeeded())
                {
                    SchedulerConfigStore.getStore().deleteItem(entity.getLong(ID));

                    JobScheduler.removeJob(entity.getLong(ID));
                }
            });
        }

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var item = EventPolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var context = item.getJsonObject(POLICY_CONTEXT);

            if (context.getJsonArray(ENTITIES) == null || context.getJsonArray(ENTITIES).isEmpty())
            {
                response.put(Entity.EVENT_SOURCE.getName(), EventSourceConfigStore.getStore().getItemsByMultiValueField(EVENT_TYPE, item.getString(POLICY_TYPE).toLowerCase()));
            }
            else
            {
                if (context.getString(ENTITY_TYPE).equalsIgnoreCase(EVENT_SOURCE))
                {
                    response.put(Entity.EVENT_SOURCE.getName(), EventSourceConfigStore.getStore().getItemsByValues(EVENT_SOURCE, context.getJsonArray(ENTITIES)));
                }

                else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE))
                {
                    response.put(Entity.EVENT_SOURCE.getName(), EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(PLUGIN_ID, new JsonArray(LogParserConfigStore.getStore().getItems().stream().map(JsonObject::mapFrom).filter(logParser -> context.getJsonArray(ENTITIES).contains(logParser.getString(LogParser.LOG_PARSER_SOURCE_TYPE))).map(logParser -> logParser.getInteger(PLUGIN_ID)).toList())));
                }

                else
                {
                    response.put(Entity.EVENT_SOURCE.getName(), EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, context.getJsonArray(ENTITIES)));
                }
            }


        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var ids = EventPolicyConfigStore.getStore().getIds();

            for (var index = 0; index < ids.size(); index++)
            {
                var item = EventPolicyConfigStore.getStore().getItem(ids.getLong(index));

                var context = item.getJsonObject(POLICY_CONTEXT);

                if (context.getJsonArray(ENTITIES) == null || context.getJsonArray(ENTITIES).isEmpty())
                {
                    response.put(CommonUtil.getString(ids.getLong(index)), EventSourceConfigStore.getStore().flatItemsByMultiValueField(EVENT_TYPE, item.getString(POLICY_TYPE).toLowerCase(), EVENT_SOURCE).size());
                }

                else
                {

                    if (context.getString(ENTITY_TYPE).equalsIgnoreCase(EVENT_SOURCE))
                    {
                        response.put(CommonUtil.getString(ids.getLong(index)), context.getJsonArray(ENTITIES).size());
                    }

                    else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE))
                    {
                        response.put(CommonUtil.getString(ids.getLong(index)), EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(PLUGIN_ID, new JsonArray(LogParserConfigStore.getStore().getItems().stream().map(JsonObject::mapFrom).filter(logParser -> context.getJsonArray(ENTITIES).contains(logParser.getString(LogParser.LOG_PARSER_SOURCE_TYPE))).map(logParser -> logParser.getInteger(PLUGIN_ID)).toList())).size());
                    }

                    else
                    {
                        response.put(CommonUtil.getString(ids.getLong(index)), EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, context.getJsonArray(ENTITIES)).size());
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var table = schema.getString(APIConstants.ENTITY_TABLE);

                this.beforeDelete(routingContext).compose(handler ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                var valid = true;

                                if (item.getString(DBConstants.FIELD_TYPE) != null && item.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
                                {
                                    valid = false;

                                    var message = String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, schema.getString(APIConstants.ENTITY_NAME));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.MESSAGE, message).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                    promise.fail(message);

                                    Bootstrap.vertx().eventBus().send(EVENT_AUDIT,
                                            new JsonObject().put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(ENTITY_TABLE, table)
                                                    .put(REQUEST, APIConstants.REQUEST_CREATE).put(MESSAGE, message).put(STATUS, Boolean.FALSE));

                                }

                                if (valid)
                                {
                                    Bootstrap.configDBService().update(table,
                                            new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), item.put(POLICY_ARCHIVED, YES), routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                            result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (!result.result().isEmpty())
                                                    {
                                                        this.configStore.updateItem(id);

                                                        promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));

                                                    }
                                                    else
                                                    {
                                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_FAIL));

                                                        promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN));
                                                    }
                                                }
                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                    promise.fail(result.cause());

                                                }
                                            });
                                }
                            }

                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.EVENT_POLICY.getName()))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.EVENT_POLICY.getName()));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    private void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(POLICY_STATE);

        var policyId = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(DBConstants.TBL_EVENT_POLICY, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, policyId),
                    new JsonObject().put(POLICY_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            EventPolicyConfigStore.getStore().updateItem(policyId).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, policyId).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));
                                }
                            });

                            this.send(routingContext, new JsonObject().put(MESSAGE, EventPolicyConfigStore.getStore().getItem(policyId).getString(POLICY_NAME) + (state.equalsIgnoreCase(YES) ? " Enabled" : " Disabled") + " successfully...").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, policyId));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(MESSAGE, "Failed to " + (state.equalsIgnoreCase(YES) ? "Enable" : "Disable") + " policy " + EventPolicyConfigStore.getStore().getItem(policyId).getString(POLICY_NAME)).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, policyId));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var params = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            this.beforeGetAll(routingContext).compose(handler ->

                    Future.<JsonArray>future(promise ->

                            this.getEntityCountPreHook(new JsonObject()).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    var items = this.configStore.getItemsByValues(params.getString(KEY), params.getJsonArray(VALUE));

                                    var entities = new JsonArray();

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = CommonUtil.removeSensitiveFields(items.getJsonObject(index), true);

                                        if (result.result() != null && result.result().containsKey(CommonUtil.getString(item.getLong(ID))))
                                        {
                                            item.put(ENTITY_PROPERTY_COUNT, result.result().getInteger(CommonUtil.getString(item.getLong(ID))));
                                        }

                                        entities.add(item);
                                    }

                                    promise.complete(entities);
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                    promise.fail(result.cause());
                                }
                            })).compose(entities -> this.afterGetAll(entities, routingContext)));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

}
