/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     14-Feb-2025     Chandresh      MOTADATA-4822: Added secondary LDAP host support in authentication flow
 *     28-Feb-2025     Smit           MOTADATA-4956: Rule Based Tagging Support
 *     3-Mar-2025      Chandresh      MOTADATA-4822: ldap.server.host -> ldap.server.primary.host
 *     4-Mar-2025      Bharat         MOTADATA-4740: Two factor authentication 2FA
 *     21-Mar-2025     Vismit         MOTADATA-5094: Added scheduler functionality for Config Upgrade Ops
 *     24-Mar-2025     <PERSON><PERSON>    M<PERSON>AD<PERSON>A-5432: Tuned reuseport, tcpkeepalive, acceptbacklog options for HTTP connections & also made web socket message size configurable
 *     25-Mar-2025     Arpit          MOTADATA-5549: VAPT, unsafe-inline execution removed, permissions header added
 *     2-Apr-2025      Bharat         MOTADATA-5637: Domain Mapping in Flow
 *     4-Mar-2025      Pruthviraj     MOTADATA-5285: Endpoints added of for netroute and netroute policy
 *     22-Apr-2025     Bharat         MOTADATA-5822: Metric Explorer Enhancements
 *     24-Apr-2025     Sankalp        MOTADATA-5945: Fixed flow of login with temporary password in case of expired password
 *     2-May-2025      Aagam          MOTADATA-6010: Added support of Open LDAP
 *     23-May-2025     Priyansh       MOTADATA-6342: Made Inactive Session timer configurable
 *     24-Jun-2025	   Pruthvi		  MOTADATA-6580 : removed encryption/decryption of sensitive field. Handled in configdbserviceimpl.
 *     24-Jun-2025     Yash Tiwari    MOTADATA-6528 : refactored getPermission method & added functionality to support explorers permission filtration
 *     24-Jul-2025     Bharat         MOTADATA-5680: VAPT - User password management
 *     23-Jul-2025	   Nikunj Patel   MOTADATA-6236: Endpoints added for DNS-servers.
 * */

package com.mindarray.api;

import com.google.common.base.Strings;
import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.unboundid.ldap.sdk.LDAPConnection;
import io.vertx.codegen.annotations.Nullable;
import io.vertx.core.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.file.OpenOptions;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.http.HttpServerOptions;
import io.vertx.core.http.HttpServerRequest;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.PemKeyCertOptions;
import io.vertx.core.streams.Pump;
import io.vertx.ext.auth.AuthProvider;
import io.vertx.ext.auth.JWTOptions;
import io.vertx.ext.auth.PubSecKeyOptions;
import io.vertx.ext.auth.User;
import io.vertx.ext.auth.authentication.AuthenticationProvider;
import io.vertx.ext.auth.authorization.Authorization;
import io.vertx.ext.auth.authorization.Authorizations;
import io.vertx.ext.auth.authorization.impl.PermissionBasedAuthorizationImpl;
import io.vertx.ext.auth.impl.jose.JWK;
import io.vertx.ext.auth.impl.jose.JWT;
import io.vertx.ext.auth.jwt.JWTAuth;
import io.vertx.ext.auth.jwt.JWTAuthOptions;
import io.vertx.ext.auth.otp.OtpKey;
import io.vertx.ext.bridge.PermittedOptions;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import io.vertx.ext.web.handler.*;
import io.vertx.ext.web.handler.sockjs.*;
import io.vertx.ext.web.handler.sockjs.impl.StringEscapeUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

import javax.net.ssl.SSLHandshakeException;
import java.io.File;
import java.net.URLEncoder;
import java.nio.channels.ClosedChannelException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.CREDENTIAL_PROFILE_TEST_SUCCEEDED;
import static com.mindarray.InfoMessageConstants.LOGIN;
import static com.mindarray.InfoMessageConstants.FORGOT_PASSWORD_SUCCEEDED;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.LDAPServer.*;
import static com.mindarray.api.PersonalAccessToken.PERSONAL_ACCESS_TOKEN;
import static com.mindarray.api.User.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.SingleSignOnUtil.INVALID_RESPONSE;
import static io.vertx.core.http.HttpHeaders.CONTENT_DISPOSITION;
import static io.vertx.core.http.HttpHeaders.CONTENT_LENGTH;
import static org.apache.http.HttpStatus.*;

public class APIServer extends AbstractVerticle
{

    private static final Logger LOGGER = new Logger(APIServer.class, GlobalConstants.MOTADATA_API, "API Server");

    private static final Map<String, String> ENDPOINTS_BY_MODULE = Map.<String, String>ofEntries(
            Map.entry("token", "token"),
            Map.entry("sso", "sso"),
            Map.entry("user", "user"),
            Map.entry("user-notification", NOTIFICATION_SETTINGS),
            Map.entry("audit", AUDIT_SETTINGS),
            Map.entry("misc", MONITOR_SETTINGS),
            Map.entry("client-logo", MONITOR_SETTINGS),
            Map.entry("settings/" + "users", USER_SETTINGS),
            Map.entry("settings/" + "user-roles", USER_SETTINGS),
            Map.entry("settings/" + "groups", GROUP_SETTINGS),
            Map.entry("settings/" + "tags", MONITOR_SETTINGS),
            Map.entry("settings/" + "password-policy", USER_SETTINGS),
            Map.entry("settings/" + "discoveries", DISCOVERY_SETTINGS),
            Map.entry("settings/" + "credential-profiles", DISCOVERY_SETTINGS),
            Map.entry("settings/" + "ldap-servers", USER_SETTINGS),
            Map.entry("settings/" + "single-sign-on", USER_SETTINGS),
            Map.entry("settings/" + "remote-event-processors", MONITOR_SETTINGS),
            Map.entry("settings/" + "agents", AGENT_SETTINGS),
            Map.entry("settings/" + "snmp-device-catalogs", MONITOR_SETTINGS),
            Map.entry("settings/" + "snmp-metric-groups", MONITOR_SETTINGS),
            Map.entry("settings/" + "snmp-device-vendors", MONITOR_SETTINGS),
            Map.entry("settings/" + "objects", MONITOR_SETTINGS),
            Map.entry("settings/" + "metrics", MONITOR_SETTINGS),
            Map.entry("settings/" + "application-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "protocol-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "proxy-server", SYSTEM_SETTINGS),
            Map.entry("settings/" + "dns-server-configuration", SYSTEM_SETTINGS),
            Map.entry("settings/" + "mail-server-configuration", SYSTEM_SETTINGS),
            Map.entry("settings/" + "integrations", INTEGRATIONS),
            Map.entry("settings/" + "integration-profiles", INTEGRATIONS),
            Map.entry("settings/" + "system-processes", MONITOR_SETTINGS),
            Map.entry("settings/" + "system-services", MONITOR_SETTINGS),
            Map.entry("settings/" + "business-hours", MONITOR_SETTINGS),
            Map.entry("settings/" + "metric-plugins", PLUGIN_LIBRARY_SETTINGS),
            Map.entry("settings/" + "topology-plugins", PLUGIN_LIBRARY_SETTINGS),
            Map.entry("settings/" + "runbook-plugins", PLUGIN_LIBRARY_SETTINGS),
            Map.entry("settings/" + "snmp-trap-forwarders", SNMP_TRAP_SETTINGS),
            Map.entry("settings/" + "snmp-trap-listener-configuration", SNMP_TRAP_SETTINGS),
            Map.entry("settings/" + "sms-gateway-configuration", SYSTEM_SETTINGS),
            Map.entry("settings/" + "snmp-trap-profiles", SNMP_TRAP_SETTINGS),
            Map.entry("settings/" + "custom-monitoring-fields", MONITOR_SETTINGS),
            Map.entry("settings/" + "system-files", MONITOR_SETTINGS),
            Map.entry("settings/" + "schedulers", "scheduler-settings"),
            Map.entry("settings/" + "snmp-oid-groups", MONITOR_SETTINGS),
            Map.entry("settings/" + "snmp-oid-groups-search", MONITOR_SETTINGS),
            Map.entry("settings/" + "rebranding", SYSTEM_SETTINGS),
            Map.entry("settings/" + "mac-scanners", SYSTEM_SETTINGS),
            Map.entry("settings/" + "my-account", MY_ACCOUNT_SETTINGS),
            Map.entry("settings/" + "license", MY_ACCOUNT_SETTINGS),
            Map.entry("settings/" + "dependency-mappers", AIOPS_SETTINGS),
            Map.entry("visualization/" + "widgets", "widgets"),
            Map.entry("visualization/" + "dashboards", "dashboards"),
            Map.entry("visualization/" + "templates", "templates"),
            Map.entry("visualization/" + "metric-explorers", "metric-explorers"),
            Map.entry("visualization/" + "topology", "topology"),
            Map.entry("settings/" + "log-directory-paths", LOG_SETTINGS),
            Map.entry("settings/" + "log-parsers", LOG_SETTINGS),
            Map.entry("settings/" + "event-sources", LOG_SETTINGS),
            Map.entry("settings/" + "log-parser-plugins", LOG_SETTINGS),
            Map.entry("settings/" + "log-collectors", LOG_SETTINGS),
            Map.entry("settings/" + "flow-settings", SYSTEM_SETTINGS),
            Map.entry("settings/" + "flow-as-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "flow-domain-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "flow-geolocation-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "flow-ip-mappers", FLOW_SETTINGS),
            Map.entry("settings/" + "flow-ip-groups", FLOW_SETTINGS),
            Map.entry("settings/" + "flow-sampling-rates", FLOW_SETTINGS),
            Map.entry("settings/" + "metric-policies", POLICY_SETTINGS),
            Map.entry("settings/" + "event-policies", POLICY_SETTINGS),
            Map.entry("visualization/" + "reports", "reports"),
            Map.entry("settings/" + "data-retention-policy", SYSTEM_SETTINGS),
            Map.entry("settings/" + "backup-profiles", SYSTEM_SETTINGS),
            Map.entry("settings/" + "storage-profiles", SYSTEM_SETTINGS),
            Map.entry("settings/" + "config-templates", CONFIG),
            Map.entry("settings/" + "configurations", CONFIG),
            Map.entry("settings/" + "log-forwarders", LOG_SETTINGS),
            Map.entry("query/" + "visualization", APIConstants.QUERY),
            Map.entry("query/" + "metric", APIConstants.QUERY),
            Map.entry("settings/" + "personal-access-tokens", USER_SETTINGS),
            Map.entry("query/" + "objects", APIConstants.QUERY),
            Map.entry("system/" + "motadata-app", SYSTEM_SETTINGS),
            Map.entry("settings/" + "compliance-rules", COMPLIANCE_SETTINGS),
            Map.entry("settings/" + "compliance-benchmarks", COMPLIANCE_SETTINGS),
            Map.entry("settings/" + "compliance-policies", COMPLIANCE_SETTINGS),
            Map.entry("settings/" + "two-factor-authentication", SYSTEM_SETTINGS),
            Map.entry("settings/" + "tag-rules", SYSTEM_SETTINGS),
            Map.entry("settings/" + "netroutes", NETROUTE_SETTINGS),
            Map.entry("settings/" + "netroute-policies", POLICY_SETTINGS),
            Map.entry("settings/" + "dns-server-profiles", DNS_SERVER));

    private static final Map<String, String> EXCLUDED_MODULES = Map.of("client-logo", MONITOR_SETTINGS, "audit", AUDIT_SETTINGS, "misc", MONITOR_SETTINGS, "user-notification", NOTIFICATION_SETTINGS, "user", USER_SETTINGS);

    private static final Set<String> EXCLUDED_ENDPOINTS = Set.of("system/motadata-app/version");

    private static final Set<String> BROADCAST_EVENTS = Set.of(EventBusConstants.EVENT_USER_PING);

    private JWTAuth jwtAuth;

    private JWT jwt;

    /**
     * Creates a new SSO user in the system if they don't already exist.
     * This method is called during SSO authentication to ensure the SSO user has a corresponding local user account.
     * <p>
     * The method performs the following:
     * 1. Retrieves username from ActiveUserCacheStore using the provided ID </br>
     * 2. Checks if user already exists in UserConfigStore </br>
     * 3. If user doesn't exist: </br>
     * - Creates new user with SSO user type </br>
     * - Sets default read-only role and empty preferences </br>
     * - Saves to database and updates UserConfigStore
     */
    private static void createSSOUserIfNotExist(JsonObject requestBody, Promise<Object> asyncFuture)
    {
        requestBody.put(USER_NAME, ActiveUserCacheStore.getStore().getItem(requestBody.getString(ID)).getString(USERNAME))
                .put(USER_PASSWORD, EMPTY_VALUE); /* in case of SSO we don't store the password */

        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, ActiveUserCacheStore.getStore().getItem(requestBody.getString(ID)).getString(USERNAME));

        if (Objects.isNull(item))
        {
            Bootstrap.configDBService().save(DBConstants.TBL_USER, new JsonObject().put(USER_TYPE, USER_TYPE_SSO)
                            .put(USER_NAME, ActiveUserCacheStore.getStore().getItem(requestBody.getString(ID)).getString(USERNAME))
                            .put(FIELD_TYPE, ENTITY_TYPE_USER).put(USER_PREFERENCES, new JsonObject()).put(USER_ROLE, UserRole.USER_ROLE_READ_ONLY)
                            .put(USER_GROUPS, new JsonArray()).put(USER_STATUS, YES),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {

                        if (result.succeeded())
                        {
                            UserConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    asyncFuture.complete();
                                }
                                else
                                {
                                    LOGGER.warn("failed to update config store user...");

                                    asyncFuture.fail("failed to update config store user...");
                                }
                            });
                        }
                        else
                        {
                            LOGGER.warn("failed to save user...");

                            asyncFuture.fail("failed to save user...");
                        }
                    });
        }
        else
        {
            asyncFuture.complete();
        }
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var server = vertx.createHttpServer(new HttpServerOptions()
                .setUseAlpn(true) //http2
                .setTcpKeepAlive(true)
                .setReuseAddress(true)
                .setReusePort(true) // Allow multiple instances to reuse the port
                .setAcceptBacklog(MotadataConfigUtil.getWebSocketBacklogQueueSize()) // Allow 1024 pending connections in backlog
                .setIdleTimeout(300) // Keep connections alive for 5 minutes
                .setCompressionLevel(6) // Explicit compression level
                .setEnabledSecureTransportProtocols(Set.of("TLSv1.2", "TLSv1.3"))
                .setSsl(MotadataConfigUtil.httpsEnabled())
                .setCompressionSupported(true)
                .setMaxWebSocketMessageSize(MotadataConfigUtil.getMaxWebSocketMessageSizeBytes() * 1024 * 1024) // 10 MB message size
                .setPemKeyCertOptions(new PemKeyCertOptions()
                        .setCertPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-cert.pem")
                        .setKeyPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem")));

        var router = Router.router(vertx);

        // this disables http access and browser will now store this param and from next request onwards server will be accessed thru https only
        if (MotadataConfigUtil.httpsEnabled())
        {
            router.route().handler(HSTSHandler.create(HSTS_VALIDATION_PERIOD, true));
        }

        // to prevent browser from guessing the context type by looking at the bytes of the resource.
        router.route().handler(routingContext ->
        {
            routingContext.response().putHeader("X-Content-Type-Options", "nosniff");

            routingContext.response().putHeader("X-XSS-Protection", "1; mode=block");

            routingContext.response().putHeader("Content-Security-Policy",
                    "default-src 'self'; " +
                            "font-src 'self' data:; " +
                            "img-src 'self' data:; " +
                            "script-src 'self' 'unsafe-eval'; " +
                            "style-src 'self' 'unsafe-inline'; " +
                            "frame-ancestors 'self'");

            routingContext.response().putHeader("X-Frame-Options", "SAMEORIGIN");

            routingContext.response().putHeader("Cache-Control", "no-cache");

            routingContext.response().putHeader("Referrer-Policy", "strict-origin-when-cross-origin");

            // Prevent other sites from using Motadata resources (e.g., images, scripts, iframes)
            routingContext.response().putHeader("Cross-Origin-Resource-Policy", "same-origin");

            // Isolates this page into a separate browsing context group, preventing window.opener access.
            // xyz.com can open motadata.com but cannot open window.opener object on motadata.com, this will stop phishing attack
            routingContext.response().putHeader("Cross-Origin-Opener-Policy", "same-origin");

            // when requesting script from third party request will be made after removing cookie
            routingContext.response().putHeader("Cross-Origin-Embedder-Policy", "credentialless");

            routingContext.response().putHeader("Permissions-Policy",
                    "geolocation=(), microphone=(), camera=(), payment=()");

            routingContext.next();
        });

        router.route("/eventbus/*").handler(this.corsHandler())
                .failureHandler(routingContext ->
                {
                    LOGGER.fatal("Somebody tried to access the api from : " + routingContext.request().getHeader("Origin"));
                    routingContext.response()
                            .setStatusCode(SC_FORBIDDEN).end();
                })
                .subRouter(eventBusHandler());

        if (MotadataConfigUtil.enableHTTPLogging())
        {
            router.route().handler(new RequestLogger()); //log API request

        }

        var bodyHandler = BodyHandler.create().setUploadsDirectory(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS).setBodyLimit(MAX_BODY_LIMIT_BYTES);

        if (!MotadataConfigUtil.corsEnabled())
        {
            router.route().handler(CorsHandler.create(".*") //Allow cross-origin request from localhost from any port. For dev.
                    .allowedMethod(HttpMethod.GET)
                    .allowedMethod(HttpMethod.POST)
                    .allowedMethod(HttpMethod.PUT)
                    .allowedMethod(HttpMethod.DELETE)
                    .allowedHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS.toString())
                    .allowedHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN.toString())
                    .allowedHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS.toString())
                    .allowedHeader(HttpHeaders.AUTHORIZATION.toString())
                    .allowedHeader(HttpHeaders.CONTENT_TYPE.toString()));
        }

        vertx.eventBus().<JsonObject>localConsumer(EVENT_AUTH_TOKEN_CREATE, message ->
        {
            try
            {
                var item = UserConfigStore.getStore().getItem(message.body().getLong(ID));

                if (item != null)
                {
                    var sessionId = UUID.randomUUID().toString().toLowerCase().trim();

                    var token = jwtAuth.generateToken(
                            new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                                    .put(GlobalConstants.ID, item.getLong(ID))
                                    .put(SESSION_ID, sessionId)
                                    .put(USER_PERMISSIONS, UserRoleConfigStore.getStore().getItem(UserConfigStore.getStore().getItem(item.getLong(ID)).getLong(USER_ROLE)).getJsonArray(UserRole.USER_ROLE_CONTEXT).add("user:" + RequestType.POST.getName()).add("token:" + RequestType.POST.getName())),
                            new JWTOptions()
                                    .setAlgorithm(ALGO_RS512)      //access token is signed and verified using RS512 while refresh token uses RS256
                                    .setSubject(API_VERSION)
                                    .setIssuer(API_AUTHOR)
                                    .setExpiresInMinutes(MotadataConfigUtil.devMode() ? 120 : MAX_ACCESS_TOKEN_VALIDITY_TIME_MINUTES));

                    message.reply(new JsonObject().put(AUTH_ACCESS_TOKEN, token).put(SESSION_ID, sessionId));
                }

                else
                {
                    message.fail(NOT_AVAILABLE, LOGIN_FAILED_USER_NOT_FOUND);
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, exception.getMessage());
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_PAT_CREATE, message ->
        {
            try
            {
                var event = message.body();

                var item = UserConfigStore.getStore().getItem(event.getLong(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_USER));

                if (item != null)
                {
                    var token = jwtAuth.generateToken(
                            new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                                    .put(GlobalConstants.ID, item.getLong(ID))
                                    .put(TOKEN_TYPE, TokenType.PERSONAL_ACCESS_TOKEN.getName())
                                    .put(USER_PERMISSIONS, UserRoleConfigStore.getStore().getItem(UserConfigStore.getStore().getItem(item.getLong(ID)).getLong(USER_ROLE)).getJsonArray(UserRole.USER_ROLE_CONTEXT).add("query:" + RequestType.GET.getName()).add("query:" + RequestType.POST.getName()).add("user:" + RequestType.POST.getName()).add("token:" + RequestType.POST.getName())),
                            new JWTOptions()
                                    .setAlgorithm(ALGO_RS512)
                                    .setSubject(API_VERSION)
                                    .setIssuer(API_AUTHOR)
                                    .setExpiresInMinutes(event.getInteger(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_VALIDITY)));

                    message.reply(new JsonObject().put(PERSONAL_ACCESS_TOKEN, token));
                }
                else
                {
                    message.fail(NOT_AVAILABLE, LOGIN_FAILED_USER_NOT_FOUND);
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, exception.getMessage());
            }
        });

        /*else
        {
            // do not allow proxies to cache the data
          router.route().handler(ctx->ctx.response().headers().add("Cache-Control", "no-store, no-cache")
                // prevents Internet Explorer from MIME - sniffing a
                // response away from the declared content-type
                .add("X-Content-Type-Options", "nosniff")
                // Strict HTTPS (for about ~6Months)
                .add("Strict-Transport-Security", "max-age=" + 15768000)
                // IE8+ do not allow opening of attachments in the context of this resource
                .add("X-Download-Options", "noopen")
                // enable XSS for IE
                .add("X-XSS-Protection", "1; mode=block")
                // deny frames
                .add("X-FRAME-OPTIONS", "DENY"));
        }*/

        //Auth provider used to validate user by verifying credentials (For normal user) and Additional requirements for LDAP user
        AuthenticationProvider authProvider = (authContext, result) ->
        {

            var username = authContext.getString(USER_NAME);

            if (username == null)
            {
                result.handle(Future.failedFuture(LOGIN_FAILED_USER_NAME_MISSING));

                return;
            }

            var password = authContext.getString(USER_PASSWORD);

            if (password == null)
            {
                result.handle(Future.failedFuture(LOGIN_FAILED_PASSWORD_MISSING));

                return;
            }

            var userValidationPromise = Promise.<Boolean>promise();

            var userId = new AtomicLong();

            fetchUser(username).onComplete(asyncResult ->
            {
                if (asyncResult.succeeded())
                {
                    userId.set(asyncResult.result().getLong(ID));

                    if (USER_TYPE_LDAP.equals(asyncResult.result().getString(USER_TYPE)))
                    {
                        try
                        {
                            validateLDAPUser(LDAPServerConfigStore.getStore().getItem(asyncResult.result().getLong(USER_LDAP_SERVER)), asyncResult.result(), password, userValidationPromise);
                        }
                        catch (Exception exception)
                        {
                            userValidationPromise.fail(String.format(LOGIN_FAILED_INVALID_LDAP_USER, exception.getMessage()));
                        }
                    }
                    else if (USER_TYPE_SSO.equals(authContext.getString(USER_TYPE)))
                    {
                        userValidationPromise.complete(true);
                    }
                    else
                    {
                        if (password.equals(asyncResult.result().getString(USER_PASSWORD)))
                        {
                            userValidationPromise.complete(true); // True, User is authorise to enter application
                        }
                        else if (CommonUtil.isNotNullOrEmpty(asyncResult.result().getString(USER_TEMPORARY_PASSWORD))
                                && password.equals(asyncResult.result().getString(USER_TEMPORARY_PASSWORD)))
                        {
                            userValidationPromise.complete(false); // False, User is not authorise to enter the application.
                        }
                        else
                        {
                            userValidationPromise.fail(LOGIN_FAILED_INVALID_CREDENTIALS);
                        }
                    }
                }

                else
                {
                    result.handle(Future.failedFuture(asyncResult.cause()));
                }
            });

            userValidationPromise.future().onComplete(asyncResult ->
            {
                if (asyncResult.succeeded())
                {
                    result.handle(Future.succeededFuture(new User()
                    {
                        @Override
                        public JsonObject attributes()
                        {
                            return null;
                        }

                        @Override
                        public boolean expired()
                        {
                            return false;
                        }

                        @Override
                        public boolean expired(int leeway)
                        {
                            return false;
                        }

                        @Override
                        public <T> @Nullable T get(String key)
                        {
                            return null;
                        }

                        @Override
                        public boolean containsKey(String key)
                        {
                            return false;
                        }

                        @Override
                        public Authorizations authorizations()
                        {
                            return null;
                        }

                        @Override
                        public User isAuthorized(Authorization authority, Handler<AsyncResult<Boolean>> handler)
                        {
                            return null;
                        }

                        @Override
                        public JsonObject principal()
                        {
                            return new JsonObject().put(USER_NAME, username)
                                    .put(GlobalConstants.ID, userId.longValue())
                                    .put(SESSION_ID, authContext.getString(SESSION_ID))
                                    .put(USER_TEMPORARY_PASSWORD, !asyncResult.result());
                        }

                        @Override
                        public void setAuthProvider(AuthProvider authProvider)
                        {

                        }

                        @Override
                        public User merge(User other)
                        {
                            return null;
                        }
                    }));
                }
                else
                {
                    result.handle(Future.failedFuture(asyncResult.cause().getMessage()));
                }
            });
        };

        router.route().handler(StaticHandler.create("dist/index.html"));

        var apiRouter = Router.router(vertx);

        var publicKeyBuffer = vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "public-key.pem");

        var secretKeyBuffer = vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem");

        jwtAuth = JWTAuth.create(vertx, new JWTAuthOptions()
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(publicKeyBuffer))
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(secretKeyBuffer))); // To sign Access token

        apiRouter.route().handler(JWTAuthHandler.create(jwtAuth));

        jwt = new JWT().addJWK(new JWK(new PubSecKeyOptions().setAlgorithm(ALGO_RS256).setBuffer(publicKeyBuffer)))
                .addJWK(new JWK(new PubSecKeyOptions().setAlgorithm(ALGO_RS256).setBuffer(secretKeyBuffer))); //To sign refresh token

        router.post("/api/v1/token").handler(bodyHandler).handler(this::statusHandler).handler(routingContext ->
        {
            try
            {
                var requestBody = routingContext.body().asJsonObject();

                var token = requestBody.getString(AUTH_REFRESH_TOKEN);

                if (token != null) //If refresh token is present then create a new token using that
                {
                    authenticate(routingContext, token, requestBody);
                }
                else
                {
                    var asyncFuture = Promise.promise();

                    var isSSOUser = CommonUtil.isNotNullOrEmpty(requestBody.getString(ID))
                            && !ActiveUserCacheStore.getStore().getItem(requestBody.getString(ID)).isEmpty();
                    // means the token request is received for the SSO User
                    if (isSSOUser)
                    {
                        createSSOUserIfNotExist(requestBody, asyncFuture);

                    }
                    else
                    {
                        asyncFuture.complete();
                    }

                    //Generate access and refresh token pair if user is valid
                    asyncFuture.future().onComplete(
                            asyncResponse ->
                            {
                                if (asyncResponse.succeeded())
                                {
                                    validateUser(requestBody.getString(USER_NAME)).onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            var sessionId = UUID.randomUUID().toString().toLowerCase().trim();

                                            var credentials = new JsonObject()
                                                    .put(USER_NAME, requestBody.getValue(USER_NAME))
                                                    .put(USER_PASSWORD, requestBody.getValue(USER_PASSWORD))
                                                    .put(SESSION_ID, sessionId);

                                            if (isSSOUser)
                                            {
                                                credentials.put(USER_TYPE, USER_TYPE_SSO); // specifying user type here as in case of sso if a system user with same username exist login is allowed
                                            }

                                            authProvider.authenticate(credentials, result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (isPasswordExpired(result.result().principal().getLong(ID), result.result().principal().getBoolean(USER_TEMPORARY_PASSWORD, false)))
                                                    {
                                                        // if user login successfully discard old login attempt count
                                                        updateLoginAttempts(UserConfigStore.getStore().getItemByValue(USER_NAME,requestBody.getString(USER_NAME)),true);

                                                        if (YES.equalsIgnoreCase(TwoAuthenticationConfigStore.getStore().getItem().getString(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE)))
                                                        {
                                                            authenticate(routingContext, result, requestBody, sessionId, isSSOUser);
                                                        }
                                                        else
                                                        {
                                                            if (isSSOUser)
                                                            {
                                                                ActiveUserCacheStore.getStore().deleteItem(requestBody.getString(ID));
                                                            }

                                                            generateToken(routingContext, result, sessionId, requestBody);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        var user = UserConfigStore.getStore().getItemByValue(USER_NAME,requestBody.getString(USER_NAME));

                                                        // update login attempts
                                                        updateLoginAttempts(user,false);

                                                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                                .setStatusCode(SC_BAD_REQUEST).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_PASSWORD_EXPIRED).put(STATUS, STATUS_FAIL).put(MESSAGE, InfoMessageConstants.PASSWORD_EXPIRED).put("login.attempts.left",MAX_LOGIN_ATTEMPTS - user.getInteger(USER_LOGIN_ATTEMPTS, 0)).encodePrettily());

                                                        vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, InfoMessageConstants.PASSWORD_EXPIRED)
                                                                .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                                    }
                                                }
                                                else
                                                {
                                                    var user = UserConfigStore.getStore().getItemByValue(USER_NAME,requestBody.getString(USER_NAME));

                                                    // update login attempts
                                                    updateLoginAttempts(user,false);

                                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(result.cause().getMessage())).put(MESSAGE, result.cause().getMessage()).put("login.attempts.left",MAX_LOGIN_ATTEMPTS - user.getInteger(USER_LOGIN_ATTEMPTS, 0)).encodePrettily());

                                                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, result.cause().getMessage())
                                                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                                }
                                            });
                                        }
                                        else
                                        {
                                            routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                    .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(ERROR_CODE, getErrorCode(response.cause().getMessage())).put(MESSAGE, response.cause().getMessage()).encodePrettily());

                                            vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, response.cause().getMessage())
                                                    .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn("Failed authentication");

                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(ERROR_CODE, getErrorCode(asyncResponse.cause().getMessage())).
                                                    put(MESSAGE, asyncResponse.cause()).encodePrettily());
                                }
                            }
                    );

                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(exception.getMessage())).put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getLocalizedMessage())).encodePrettily());

                vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, exception.getMessage())
                        .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
            }
        });

        router.get("/api/v1/login-metadata").handler(this::statusHandler).handler(routingContext ->
        {
            var item = TwoAuthenticationConfigStore.getStore().getItem();

            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject()
                    .put("saml.configured", SingleSignOnUtil.getSAMLClient() != null)
                    .put("2fa.configured", item.getString(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_ENABLE))
                    .put("2fa.method", item.getString(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE))
                    .encode());
        });

        /**
         * For SP initiated login this is the entry point
         */
        router.get("/api/v1/sso").handler(this::statusHandler).handler(routingContext ->
        {

            try
            {
                var samlClient = SingleSignOnUtil.getSAMLClient();

                if (Objects.isNull(samlClient))
                {
                    LOGGER.info("Invalid Configuration");

                    routingContext.response()
                            .setStatusCode(SC_MOVED_TEMPORARILY)
                            .putHeader("Location", SingleSignOnUtil.getServerURL() + "/?message=" + INVALID_RESPONSE)
                            .end();

                    return;
                }

                var redirectURL = samlClient.getSamlRequest();

                if (Strings.isNullOrEmpty(redirectURL))
                {
                    LOGGER.info("Invalid redirect URL from IDP");

                    routingContext.response()
                            .setStatusCode(SC_MOVED_TEMPORARILY)
                            .putHeader("Location", SingleSignOnUtil.getServerURL() + "/?message=" + INVALID_RESPONSE)
                            .end();
                    return;
                }

                routingContext.response().putHeader("Cache-Control", "no-cache, no-store").putHeader("Pragma", "no-cache")
                        .putHeader("Content-Type", CONTENT_TYPE_TEXT_HTML).end(
                                String.format(SingleSignOnUtil.REDIRECT_TEMPLATE,
                                        org.apache.commons.text.StringEscapeUtils.escapeHtml4(samlClient.getIdentityProviderUrl()),
                                        org.apache.commons.text.StringEscapeUtils.escapeHtml4(SingleSignOnUtil.SAML_REQUEST),
                                        org.apache.commons.text.StringEscapeUtils.escapeHtml4(SingleSignOnUtil.SAML_REQUEST),
                                        org.apache.commons.text.StringEscapeUtils.escapeHtml4(redirectURL)));

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                routingContext.response()
                        .setStatusCode(SC_MOVED_TEMPORARILY)
                        .putHeader("Location", SingleSignOnUtil.getServerURL() + "/?message=" + INVALID_RESPONSE)
                        .end();
            }

        });

        /**
         * This method will be used for receiving response from the IDP - SAMLResponse
         */
        router.post("/api/v1/sso/callback").handler(bodyHandler).handler(this::statusHandler).handler(routingContext ->
        {
            try
            {
                var samlClient = SingleSignOnUtil.getSAMLClient();

                if (Objects.isNull(samlClient))
                {
                    LOGGER.info("Invalid SSO Configuration");

                    routingContext.response()
                            .setStatusCode(SC_MOVED_TEMPORARILY)
                            .putHeader("Location", SingleSignOnUtil.getServerURL() + "/?message=" + INVALID_RESPONSE)
                            .end();

                    return;
                }

                var authenticatedUser = samlClient.decodeAndValidateSamlResponse(routingContext.request().getFormAttribute(SingleSignOnUtil.SAML_RESPONSE),
                        routingContext.request().method().name());

                if (Objects.isNull(authenticatedUser))
                {
                    LOGGER.warn(LOGIN_FAILED_INVALID_SAML_RESPONSE);

                    routingContext.response().setStatusCode(SC_MOVED_TEMPORARILY)
                            .putHeader("Location", SingleSignOnUtil.getServerURL() + "/?message=" + LOGIN_FAILED_INVALID_SAML_RESPONSE)
                            .end();

                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, DEFAULT_USER)
                            .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host())
                            .put(MESSAGE, LOGIN_FAILED_INVALID_SAML_RESPONSE)
                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));

                    return;
                }

                var sessionId = authenticatedUser.getAssertion().getAuthnStatements().getFirst().getSessionIndex();

                ActiveUserCacheStore.getStore().updateItem(sessionId,
                        new JsonObject().put(USERNAME, authenticatedUser.getNameID()));

                routingContext.response().setStatusCode(SC_MOVED_TEMPORARILY)
                        .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).putHeader("Location",
                                SingleSignOnUtil.getServerURL() + "?id=" + sessionId)
                        .end();
            }
            catch (Exception exception)
            {
                APIUtil.sendResponse(exception, routingContext);

                vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, DEFAULT_USER)
                        .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host())
                        .put(MESSAGE, LOGIN_FAILED_INVALID_SAML_RESPONSE)
                        .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
            }
        });

        /**
         * This method is called in case of IDP initiated logout
         */
        router.get("/api/v1/sso/logout").handler(bodyHandler).handler(this::statusHandler).handler(routingContext ->
        {
            try
            {
                var userName = SingleSignOnUtil.getUserName(routingContext);

                var samlClient = SingleSignOnUtil.getSAMLClient();

                samlClient.decodeAndValidateSamlLogoutRequest(routingContext.request().getParam(SingleSignOnUtil.SAML_REQUEST), userName, routingContext.request().method().name());

                if (!Strings.isNullOrEmpty(userName))
                {
                    var userId = UserConfigStore.getStore().getItemByValue(USER_NAME, userName).getLong(ID);

                    for (var token : AuthTokenConfigStore.getStore().getItemsByValue(USER_ID, userId))
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_STREAM_STOP,
                                new JsonObject().put(SESSION_ID, ((JsonObject) token).getString(SESSION_ID)));
                    }

                    Bootstrap.configDBService().delete(TBL_TOKEN, new JsonObject().put(FIELD_NAME, USER_ID).
                            put(VALUE, userId), userName, "localhost", result ->
                    {
                        if (result.succeeded())
                        {
                            AuthTokenConfigStore.getStore().deleteItems(result.result());

                            try
                            {
                                var response = SingleSignOnUtil.getSAMLClient().
                                        getSamlLogoutResponse("urn:oasis:names:tc:SAML:2.0:status:Success", "User logged out successfully");

                                if (!Strings.isNullOrEmpty(response))
                                {
                                    LOGGER.info("User logged out successfully : " + userName);

                                    routingContext.response().putHeader("Cache-Control", "no-cache, no-store").putHeader("Pragma", "no-cache")
                                            .putHeader("Content-Type", CONTENT_TYPE_TEXT_HTML).end(
                                                    String.format(SingleSignOnUtil.REDIRECT_TEMPLATE,
                                                            org.apache.commons.text.StringEscapeUtils.escapeHtml4(SingleSignOnUtil.getLogoutURL()),
                                                            org.apache.commons.text.StringEscapeUtils.escapeHtml4(SingleSignOnUtil.SAML_RESPONSE),
                                                            org.apache.commons.text.StringEscapeUtils.escapeHtml4(SingleSignOnUtil.SAML_RESPONSE),
                                                            org.apache.commons.text.StringEscapeUtils.escapeHtml4(response)));
                                }
                            }
                            catch (Exception exception)
                            {
                                APIUtil.sendResponse(exception, routingContext);
                            }
                        }
                        else
                        {
                            LOGGER.warn(LOGOUT_FAILED_FAILED_TO_DELETE_REFRESH_TOKEN);

                            routingContext.response().setStatusCode(HttpStatus.SC_BAD_REQUEST)
                                    .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                    .end(new JsonObject().put(MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS).encode());
                        }
                    });
                }
                else
                {
                    routingContext.response().setStatusCode(HttpStatus.SC_BAD_REQUEST)
                            .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                            .end(new JsonObject().put(MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS).encode());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                APIUtil.sendResponse(exception, routingContext);
            }
        });

        var settingsAPIRouter = Router.router(vertx);

        var auditAPIRouter = Router.router(vertx);

        var userNotificationAPIRouter = Router.router(vertx);

        var miscRouter = Router.router(vertx);

        var visualizationRouter = Router.router(vertx);

        var systemRouter = Router.router(vertx);

        var queryRouter = Router.router(vertx);

        apiRouter.route("/settings/*").method(HttpMethod.GET).handler(this::authenticate).subRouter(settingsAPIRouter);

        apiRouter.route("/settings/*").handler(bodyHandler).handler(this::authenticate).subRouter(settingsAPIRouter);

        apiRouter.route("/system/*").method(HttpMethod.GET).handler(this::authenticate).subRouter(systemRouter);

        apiRouter.route("/system/*").handler(bodyHandler).handler(this::authenticate).subRouter(systemRouter);

        apiRouter.route("/audit/*").handler(this::authenticate).subRouter(auditAPIRouter);

        apiRouter.route("/user-notification/*").handler(this::authenticate).subRouter(userNotificationAPIRouter);

        apiRouter.route("/misc/*").method(HttpMethod.GET).handler(this::authenticate).subRouter(miscRouter);

        apiRouter.route("/misc/*").handler(bodyHandler).handler(this::authenticate).subRouter(miscRouter);

        apiRouter.route("/visualization/*").method(HttpMethod.GET).handler(this::authenticate).subRouter(visualizationRouter);

        apiRouter.route("/visualization/*").handler(bodyHandler).handler(this::authenticate).subRouter(visualizationRouter);

        apiRouter.route("/query/*").handler(bodyHandler).handler(this::authenticate).subRouter(queryRouter);

        APIRouter.init(settingsAPIRouter, miscRouter, visualizationRouter, systemRouter, queryRouter);

        var userRouter = Router.router(vertx);

        userRouter.route();

        apiRouter.route("/user/*").method(HttpMethod.GET).handler(this::authenticate).subRouter(userRouter);

        apiRouter.route("/user/*").handler(bodyHandler).handler(this::authenticate).subRouter(userRouter);

        userRouter.post("/logout").handler(this::logout);

        router.route("/upload").handler(bodyHandler).handler(this::upload);

        router.route("/oauth-callback").handler(this::redirectOAuthCallback);

        router.route("/upload-firmware").handler(BodyHandler.create().setUploadsDirectory(ConfigConstants.CONFIG_FIRMWARE_DEFAULT_DIR).setBodyLimit(MAX_BODY_LIMIT_BYTES)).handler(this::uploadFirmwareFile);

        router.route("/upload-image").handler(bodyHandler).handler(this::uploadImage);

        router.route("/download").handler(bodyHandler).handler(this::download);

        router.get("/client-logo").handler(this::sendLogo);

        router.route("/api/v1/*").handler(this::statusHandler).subRouter(apiRouter);

        router.route("/forgot-password").handler(bodyHandler).handler(this::forgotPassword);

        router.route("/samples/*").method(HttpMethod.GET).handler(this::staticResourceHandler);

        router.route("/*").method(HttpMethod.GET).handler(StaticHandler.create(FileSystemAccess.ROOT, CURRENT_DIR + "/dist"));

        router.route("/*").method(HttpMethod.GET).handler(routingContext -> routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                .sendFile(CURRENT_DIR + "/dist/index.html"));

        router.errorHandler(SC_PRECONDITION_FAILED, routingContext ->
        {
            if (routingContext.failure() != null)
            {
                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(SC_PRECONDITION_FAILED)
                        .end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_PRECONDITION_FAILED).put(MESSAGE, routingContext.failure().getMessage()).encodePrettily());
            }
        });

        router.errorHandler(SC_INTERNAL_SERVER_ERROR, routingContext ->
        {

            if (routingContext.failure() != null)
            {
                LOGGER.error(routingContext.failure());

                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(SC_INTERNAL_SERVER_ERROR)
                        .end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR).put(MESSAGE, routingContext.failure()).encodePrettily());
            }
        });

        //To handle Messages sent to Motadata server over event bus bridge.

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SERVER, message ->
        {

            try
            {

                if (message.body() != null && !message.body().isEmpty() && CommonUtil.isNotNullOrEmpty(message.body().getString(EventBusConstants.EVENT_TYPE)) && message.body().containsKey(EventBusConstants.EVENT_CONTEXT))
                {
                    var context = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);

                    if (CommonUtil.isNotNullOrEmpty(message.body().getString(SESSION_ID)))
                    {
                        context.put(SESSION_ID, message.body().getString(SESSION_ID));
                    }

                    if (CommonUtil.isNotNullOrEmpty(message.body().getString(USER_NAME)))
                    {
                        context.put(USER_NAME, message.body().getString(USER_NAME));

                        if (Objects.isNull(AuthTokenConfigStore.getStore().getItemByValue(USER_ID, CommonUtil.getLong(UserConfigStore.getStore()
                                .getItemByValue(USER_NAME, message.body().getString(USER_NAME)).getLong(ID)))))
                        {
                            EventBusConstants.publish(UI_NOTIFICATION_USER_LOGOUT, new JsonObject()
                                    .put(USER_NAME, message.body().getString(USER_NAME)).put(USER_ID, UserConfigStore.getStore()
                                            .getItemByValue(USER_NAME, message.body().getString(USER_NAME)).getLong(ID)));
                        }
                    }

                    if (BROADCAST_EVENTS.contains(message.body().getString(EventBusConstants.EVENT_TYPE)))
                    {
                        vertx.eventBus().publish(message.body().getString(EventBusConstants.EVENT_TYPE), context);
                    }
                    else
                    {
                        vertx.eventBus().send(message.body().getString(EventBusConstants.EVENT_TYPE), context);
                    }
                }

                else
                {
                    LOGGER.warn("invalid ui message body...");

                    message.fail(NOT_AVAILABLE, EVENT_INVALID_CONTEXT);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, exception.getMessage());
            }
        });

        vertx.setPeriodic(TimeUnit.MINUTES.toMillis(30), timer ->
        {
            try
            {
                for (var item : ActiveUserCacheStore.getStore().getItems().entrySet())
                {
                    if (DateTimeUtil.convertTime(item.getValue().getLong(LAST_ACTIVE_TIMESTAMP)) > USER_SESSION_INACTIVE_TIMEOUT_SECONDS) // 1 hour
                    {
                        ActiveUserCacheStore.getStore().deleteItem(item.getKey());
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        server.requestHandler(router)
                .exceptionHandler(exception ->
                {

                    if (exception instanceof ClosedChannelException || exception instanceof SSLHandshakeException)
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(exception);
                    }
                })
                .listen(MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()), result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });
    }

    /**
     * Handles two-factor authentication (2FA) for users during the login process.
     * Supports both email-based OTP and authenticator app-based TOTP methods.
     * <p>
     * The method handles the following scenarios: </br>
     * 1. For email-based 2FA: </br>
     * - Generates and sends OTP via email </br>
     * - Validates submitted OTP within 3-minute validity period </br>
     * 2. For authenticator app-based 2FA: </br>
     * - Generates TOTP key and QR code for first-time setup </br>
     * - Validates TOTP codes for subsequent logins </br>
     * - Stores encrypted TOTP key in database </br>
     * <p>
     * The method expects the following in requestBody: </br>
     * - email.auth.method: Boolean indicating if email-based 2FA is requested </br>
     * - AUTH_TOTP: Integer containing the OTP/TOTP code </br>
     * - USER_TOTP_KEY: String containing the TOTP secret key (for first-time authenticator setup)
     */
    private void authenticate(RoutingContext routingContext, AsyncResult<User> result, JsonObject requestBody, String sessionId, boolean isSSOUser)
    {
        var item = TwoAuthenticationConfigStore.getStore().getItem();

        var totp = requestBody.getInteger(AUTH_TOTP); // TOTP for MFA

        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, requestBody.getString(USER_NAME));

        if (totp == null)// if totp is missing then send the otp to the user mail or generate the totp key
        {
            // if UI sends email.auth.method as true means user is bypassing auth app method and trying to log in with email at that point we will also generate otp
            if ((requestBody.containsKey("email.auth.method") && requestBody.getBoolean("email.auth.method")) || TwoFactorAuthentication.AuthenticationType.EMAIL.getName().equalsIgnoreCase(item.getString(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE)))
            {
                var otp = TwoFactorAuthenticationUtil.generateOTP();

                if (user.containsKey(USER_EMAIL) && !user.getString(USER_EMAIL).trim().isEmpty())
                {
                    // Update Temporary OTP and Timestamp in DB
                    Bootstrap.configDBService().update(TBL_USER,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                            new JsonObject().put(USER_ONE_TIME_PASSWORD, otp)
                                    .put(USER_ONE_TIME_PASSWORD_CREATED_TIME, System.currentTimeMillis()),
                            user.getString(USER_NAME),
                            routingContext.request().remoteAddress().host(),
                            asyncResponse ->
                            {
                                if (asyncResponse.succeeded())
                                {
                                    UserConfigStore.getStore().updateItems(asyncResponse.result()).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            Notification.sendEmail(new JsonObject()
                                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("security-lock.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, InfoMessageConstants.OTP_MAIL_SUBJECT)
                                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(USER_EMAIL)))
                                                    .put(Notification.TEMPLATE_NAME, Notification.EMAIL_USER_OTP_VERIFICATION_HTML_TEMPLATE)
                                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(USER_NAME, requestBody.getString(USER_NAME)).put(OTP, CommonUtil.getString(otp))));

                                            routingContext.response()
                                                    .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                    .end(new JsonObject().put(MESSAGE, InfoMessageConstants.TWO_FACTOR_AUTHENTICATION_OTP_SENT_SUCCEEDED).put(USER_EMAIL, user.getString(USER_EMAIL)).put(SESSION_ID, sessionId).encode());
                                            // not showing this message to user.
                                        }
                                        else
                                        {
                                            //Revert the changes
                                            Bootstrap.configDBService().update(TBL_USER,
                                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                                                    new JsonObject().put(GARBAGE_FIELDS, new JsonArray().add(USER_ONE_TIME_PASSWORD).add(USER_ONE_TIME_PASSWORD_CREATED_TIME)),
                                                    user.getString(USER_NAME),
                                                    routingContext.request().remoteAddress().host(),
                                                    asyncFuture ->
                                                    {
                                                        if (asyncFuture.succeeded())
                                                        {
                                                            if (CommonUtil.debugEnabled())
                                                            {
                                                                LOGGER.debug(String.format("reverting one time password for user id: %s done", result.result()));
                                                            }

                                                        }

                                                        else
                                                        {
                                                            LOGGER.error(new Exception(String.format("error occurred while reverting one time password for user id: %s", result.result()), result.cause()));
                                                        }

                                                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                                .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                                                .end(new JsonObject()
                                                                        .put(STATUS, STATUS_FAIL)
                                                                        .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause()))
                                                                        .encodePrettily());
                                                    });
                                        }
                                    });
                                }

                                else
                                {
                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                            .end(new JsonObject()
                                                    .put(STATUS, STATUS_FAIL)
                                                    .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResponse.cause().getMessage()))
                                                    .encodePrettily());
                                }
                            });
                }
                else
                {
                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_USER_EMAIL_NOT_SET).put(MESSAGE, "2FA via Email is enabled, but no email address is associated with your account. Please contact your administrator.").encodePrettily());
                    // Msg as per PMG
                }

            }
            else if (!user.containsKey(USER_TOTP_KEY)) // generate key only first time if it exists don't generate
            {
                vertx.<OtpKey>executeBlocking(future -> future.complete(TwoFactorAuthenticationUtil.generateAuthenticatorKey())).onComplete(
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                routingContext.response()
                                        .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                        .end(new JsonObject().put(USER_TOTP_KEY, asyncResult.result().getKey()).put("url", TwoFactorAuthenticationUtil.generateQRCode(result.result().principal().getString(USER_NAME), asyncResult.result())).put(SESSION_ID, sessionId).encode());
                            }
                            else
                            {
                                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                        .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(asyncResult.cause().getMessage())).put(MESSAGE, asyncResult.cause()).encodePrettily());

                            }
                        });

            }
            else
            {
                routingContext.response()
                        .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .end(new JsonObject()
                                .put(STATUS, STATUS_SUCCEED)
                                .put(MESSAGE, InfoMessageConstants.TWO_FACTOR_AUTHENTICATION_ALREADY_REGISTERED_USER)
                                .put(SESSION_ID, sessionId).encode());
                // Not showing this messages to UI as it's not needed.
                // it's the case where user is already registered with MFA and trying to log in with app.
            }
        }
        else
        {
            // if UI sends email.auth.method as true means user is bypassing auth app method and trying to log in with email at that point we will also generate otp
            if ((requestBody.containsKey("email.auth.method") && requestBody.getBoolean("email.auth.method")) || TwoFactorAuthentication.AuthenticationType.EMAIL.getName().equalsIgnoreCase(item.getString(TwoFactorAuthentication.TWO_FACTOR_AUTHENTICATION_MODE)))
            {
                if ((user.containsKey(USER_ONE_TIME_PASSWORD_CREATED_TIME) && (System.currentTimeMillis() - user.getLong(USER_ONE_TIME_PASSWORD_CREATED_TIME)) < TimeUnit.MINUTES.toMillis(3)))
                {
                    if (Objects.equals(user.getInteger(USER_ONE_TIME_PASSWORD), totp))
                    {
                        if (isSSOUser)
                        {
                            ActiveUserCacheStore.getStore().deleteItem(requestBody.getString(ID));
                        }

                        generateToken(routingContext, result, sessionId, requestBody);
                    }
                    else
                    {
                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(OTP_INVALID)).put(MESSAGE, OTP_INVALID).encodePrettily());

                        vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, OTP_INVALID)
                                .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                    }
                }
                else
                {
                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(OTP_EXPIRED)).put(MESSAGE, OTP_EXPIRED).encodePrettily());

                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, OTP_EXPIRED)
                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                }
            }
            else
            {
                // when user didn't set up the authenticator UI will send the secret key we shared earlier for QR generating back for registration
                // if request body doesn't have the secret key then it means user is already registered with app, and he will use same app again.
                if (requestBody.containsKey(USER_TOTP_KEY))
                {
                    TwoFactorAuthenticationUtil.validateToken(requestBody.getString(USER_TOTP_KEY), String.valueOf(totp)).onComplete(
                            asyncFuture ->
                            {
                                if (asyncFuture.succeeded())
                                {
                                    if (isSSOUser)
                                    {
                                        ActiveUserCacheStore.getStore().deleteItem(requestBody.getString(ID));
                                    }

                                    // Update user with generated totp key
                                    // stored in encrypted format
                                    // This will happen first time while registering the user auth app
                                    Bootstrap.configDBService().update(TBL_USER,
                                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                                            new JsonObject().put(USER_TOTP_KEY, requestBody.getString(USER_TOTP_KEY)),
                                            user.getString(USER_NAME),
                                            routingContext.request().remoteAddress().host(),
                                            asyncResponse ->
                                            {
                                                if (asyncResponse.succeeded())
                                                {
                                                    UserConfigStore.getStore().updateItems(asyncResponse.result()).onComplete(asyncResult ->
                                                    {
                                                        if (asyncResult.succeeded())
                                                        {
                                                            generateToken(routingContext, result, sessionId, requestBody);
                                                        }
                                                        else
                                                        {
                                                            //Revert the changes
                                                            Bootstrap.configDBService().update(TBL_USER,
                                                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                                                                    new JsonObject().put(GARBAGE_FIELDS, new JsonArray().add(USER_TOTP_KEY)),
                                                                    user.getString(USER_NAME),
                                                                    routingContext.request().remoteAddress().host(),
                                                                    asyncPromise ->
                                                                    {
                                                                        if (asyncPromise.succeeded())
                                                                        {
                                                                            if (CommonUtil.debugEnabled())
                                                                            {

                                                                                LOGGER.debug(String.format("reverting secret key for user id: %s done", asyncPromise.result()));

                                                                            }
                                                                        }

                                                                        else
                                                                        {
                                                                            LOGGER.error(new Exception(String.format("error occurred while reverting secret key for user id: %s", asyncPromise.result()), asyncPromise.cause()));
                                                                        }

                                                                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                                                .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                                                                .end(new JsonObject()
                                                                                        .put(STATUS, STATUS_FAIL)
                                                                                        .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncPromise.cause()))
                                                                                        .encodePrettily());
                                                                    });
                                                        }
                                                    });
                                                }

                                                else
                                                {
                                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                            .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                                            .end(new JsonObject()
                                                                    .put(STATUS, STATUS_FAIL)
                                                                    .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResponse.cause().getMessage()))
                                                                    .encodePrettily());
                                                }
                                            });
                                }
                                else
                                {
                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(TOTP_INVALID)).put(MESSAGE, TOTP_INVALID).encodePrettily());

                                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, TOTP_INVALID)
                                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                }
                            });

                }
                else
                {
                    // fetch secret key from DB and validate the totp
                    // stored in encrypted format
                    TwoFactorAuthenticationUtil.validateToken(user.getString(USER_TOTP_KEY), String.valueOf(totp)).onComplete(
                            asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    if (isSSOUser)
                                    {
                                        ActiveUserCacheStore.getStore().deleteItem(requestBody.getString(ID));
                                    }

                                    generateToken(routingContext, result, sessionId, requestBody);
                                }
                                else
                                {

                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_UNAUTHORIZED).put(ERROR_CODE, getErrorCode(TOTP_INVALID)).put(MESSAGE, TOTP_INVALID).encodePrettily());

                                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, TOTP_INVALID)
                                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                }
                            });
                }

            }
        }
    }

    /**
     * Authenticates a user using refresh token and manages token lifecycle.
     * Handles token validation, security checks, and token refresh operations.
     * <p>
     * The method performs the following security checks and operations:
     * 1. Validates the refresh token's signature and expiration </br>
     * 2. Verifies the token exists in AuthTokenConfigStore </br>
     * 3. Checks for token compromise by comparing remote addresses </br>
     * 4. Validates user status and password expiration </br>
     * 5. Generates new access tokens when needed
     */
    private void authenticate(RoutingContext routingContext, String token, JsonObject requestBody)
    {
        if (validToken(token))
        {
            var payload = jwt.decode(token);

            var item = AuthTokenConfigStore.getStore().getItem(token);

            if (item != null)
            {
                var authToken = item.getString(AUTH_ACCESS_TOKEN);

                jwtAuth.authenticate(new JsonObject().put("token", authToken), asyncResult ->
                {
                    if (asyncResult.succeeded() && !item.getString(GlobalConstants.REMOTE_ADDRESS).equalsIgnoreCase(routingContext.request().remoteAddress().host()))
                    {
                        LOGGER.warn(String.format("the access token compromised hence deleting the access token for user %s", payload.getString(USER_NAME)));

                        Bootstrap.configDBService().delete(TBL_TOKEN,
                                new JsonObject()
                                        .put(FIELD_NAME, TOKEN_ID)
                                        .put(VALUE, token), payload.getString(USER_NAME), SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    AuthTokenConfigStore.getStore().deleteItem(token);

                                    if (result.succeeded())
                                    {
                                        LOGGER.info(String.format("the access token deleted for user %s", payload.getString(USER_NAME)));


                                    }

                                    else
                                    {
                                        LOGGER.info(String.format("failed to delete the access tokens for user %s", payload.getString(USER_NAME)));

                                    }

                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_LOGIN_TOKEN_COMPROMISED).put(MESSAGE, LOGIN).encodePrettily());
                                });
                    }

                    else
                    {
                        validateUser(payload.getString(USER_NAME)).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                if (isPasswordExpired(payload.getLong(ID), false))
                                {
                                    var accessToken = generateAccessToken(payload.getString(USER_NAME), payload.getLong(GlobalConstants.ID), payload.getString(SESSION_ID));

                                    // Storing auth token in the cache and using it. No need to store it in MV store every time
                                    AuthTokenConfigStore.getStore().updateToken(token, accessToken);

                                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .end(new JsonObject()
                                                    .put(AUTH_ACCESS_TOKEN, accessToken)
                                                    .put(AUTH_REFRESH_TOKEN, token)
                                                    .put(SESSION_ID, payload.getString(SESSION_ID))
                                                    .encode());
                                }

                                else
                                {
                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_BAD_REQUEST).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_PASSWORD_EXPIRED).put(STATUS, STATUS_FAIL).put(MESSAGE, InfoMessageConstants.PASSWORD_EXPIRED).encodePrettily());

                                    vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, InfoMessageConstants.PASSWORD_EXPIRED)
                                            .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                                }
                            }
                            else
                            {
                                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                        .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(ERROR_CODE, getErrorCode(result.cause().getMessage())).put(MESSAGE, result.cause().getMessage()).encodePrettily());

                                vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, result.cause().getMessage())
                                        .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                            }
                        });
                    }
                });
            }

            else
            {
                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_LOGIN_INVALID_REFRESH_TOKEN).put(MESSAGE, LOGIN_FAILED_INVALID_REFRESH_TOKEN).encodePrettily());
            }

        }

        else
        {
            LOGGER.warn("login warning : refresh token expired...");

            Bootstrap.configDBService().delete(TBL_TOKEN,
                    new JsonObject().put(FIELD_NAME, TOKEN_ID).put(VALUE, token), routingContext.user() != null && routingContext.user().principal().getString(USER_NAME) != null ? routingContext.user().principal().getString(USER_NAME) : DEFAULT_USER, routingContext.request().remoteAddress().host(),
                    future -> routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                            .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(MESSAGE, LOGIN_FAILED_INVALID_REFRESH_TOKEN).put(ERROR_CODE, ErrorCodes.ERROR_CODE_LOGIN_INVALID_REFRESH_TOKEN).encodePrettily()));

            AuthTokenConfigStore.getStore().deleteItem(token);
        }
    }

    /**
     * Generates and stores authentication tokens for a successfully authenticated user.
     * Creates both access token and refresh token, stores them in the database and token store,
     * and returns them to the client.
     * <p>
     * The method performs the following operations:
     * 1. Generates a new access token </br>
     * 2. Creates a signed refresh token using RS256 algorithm </br>
     * 3. Stores tokens in database with user and session information </br>
     * 4. Updates AuthTokenConfigStore </br>
     * 5. Sends audit event for successful/failed token generation
     */
    private void generateToken(RoutingContext routingContext, AsyncResult<User> result, String sessionId, JsonObject requestBody)
    {
        var accessToken = generateAccessToken(result.result().principal().getString(USER_NAME), result.result().principal().getLong(GlobalConstants.ID), sessionId);

        var refreshToken = jwt.sign(new JsonObject()
                        .put(USER_NAME, result.result().principal().getString(USER_NAME))
                        .put(GlobalConstants.ID, result.result().principal().getLong(GlobalConstants.ID))
                        .put(SESSION_ID, sessionId),
                new JWTOptions()
                        .setAlgorithm(ALGO_RS256)
                        .setSubject(API_VERSION)
                        .setIssuer(API_AUTHOR)
                        .setExpiresInMinutes(MAX_REFRESH_TOKEN_VALIDITY_TIME_MINUTES));

        Bootstrap.configDBService().save(TBL_TOKEN,
                new JsonObject()
                        .put(TOKEN_ID, refreshToken)
                        .put(TOKEN_USER, result.result().principal().getLong(GlobalConstants.ID))
                        .put(SESSION_ID, sessionId)
                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentMilliSeconds())
                        .put(AUTH_ACCESS_TOKEN, accessToken).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()), requestBody.getString(USER_NAME), SYSTEM_REMOTE_ADDRESS,
                future ->
                {
                    if (future.succeeded())
                    {
                        AuthTokenConfigStore.getStore().addItem(future.result()).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                routingContext.response()
                                        .putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                        .end(new JsonObject()
                                                .put(AUTH_ACCESS_TOKEN, accessToken)
                                                .put(AUTH_REFRESH_TOKEN, refreshToken)
                                                .put(SESSION_ID, sessionId)
                                                .put(USER_TEMPORARY_PASSWORD, result.result().principal().getBoolean(USER_TEMPORARY_PASSWORD, false) ? YES : NO)
                                                .encode()
                                        );

                                vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, String.format(InfoMessageConstants.AUDIT_USER_LOGIN, requestBody.getString(USER_NAME, EMPTY_VALUE), routingContext.request().remoteAddress().host()))
                                        .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.TRUE));
                            }

                            else
                            {
                                routingContext.response()
                                        .putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                        .setStatusCode(SC_UNAUTHORIZED)
                                        .end(new JsonObject().put(MESSAGE, LOGIN_FAILED_TOKEN_SAVE_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_SAVE).encodePrettily());

                                vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, LOGIN_FAILED_TOKEN_SAVE_FAILED)
                                        .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));
                            }
                        });

                    }

                    else
                    {
                        routingContext.response()
                                .putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                .setStatusCode(SC_UNAUTHORIZED)
                                .end(new JsonObject().put(MESSAGE, LOGIN_FAILED_TOKEN_CREATE_FAILED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DATABASE_SAVE).encodePrettily());

                        vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, requestBody.getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, LOGIN_FAILED_TOKEN_CREATE_FAILED)
                                .put(REQUEST, APIConstants.REQUEST_LOGIN).put(STATUS, Boolean.FALSE));

                    }
                });
    }


    //as of now we are going to disable api access from outside... // only Motadata UI can access API ... no one else can access it...
    private void authenticate(RoutingContext routingContext)
    {
        try
        {
            var cookie = routingContext.request().getCookie(CLIENT_ID);

            if (cookie != null && cookie.getValue().equalsIgnoreCase(UI_CLIENT_ID))
            {
                if (routingContext.user() != null)
                {
                    validateUser(routingContext).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            var request = routingContext.request();

                            if (request.path() != null)
                            {
                                // for pat user, it is not required for user to log in every time.

                                if ((routingContext.user().get(TOKEN_TYPE) == null || !routingContext.user().get(TOKEN_TYPE).toString().equalsIgnoreCase(TokenType.PERSONAL_ACCESS_TOKEN.getName())) && Objects.isNull(AuthTokenConfigStore.getStore()
                                        .getItemByValue(USER_ID, CommonUtil.getLong(routingContext.user().get(ID)))))
                                {
                                    EventBusConstants.publish(UI_NOTIFICATION_USER_LOGOUT, new JsonObject()
                                            .put(USER_NAME, routingContext.user().get(USER_NAME)).put(USER_ID, routingContext.user().get(ID)));

                                    routingContext.clearUser();

                                    routingContext.response()
                                            .setStatusCode(SC_MOVED_TEMPORARILY)
                                            .putHeader("Location", "/")
                                            .end();

                                    return;
                                }

                                var tokens = request.path().replace(routingContext.mountPoint(), EMPTY_VALUE).split("/");

                                var authorizations = routingContext.user().authorizations();

                                var valid = false;

                                if (EXCLUDED_ENDPOINTS.contains(request.path().replace(routingContext.mountPoint(), EMPTY_VALUE).toLowerCase()))
                                {
                                    valid = true;
                                }
                                else if (authorizations != null && authorizations.get("jwt-authentication") != null)
                                {
                                    var visualizationModule = EXCLUDED_MODULES.containsKey(tokens[0].trim());

                                    var path = visualizationModule ? getPermission(tokens[0].trim(), request, routingContext.body().asJsonObject(), false)
                                            : getPermission(tokens.length > 1 ? tokens[0].trim() + "/" + tokens[1].trim() : tokens[0].trim(), request, routingContext.body().asJsonObject(), false);

                                    for (var authorization : authorizations.get("jwt-authentication"))
                                    {
                                        if (((PermissionBasedAuthorizationImpl) authorization).getPermission().matches(path) || visualizationModule)
                                        {
                                            valid = true;

                                            break;
                                        }
                                    }
                                }

                                if (valid)
                                {
                                    routingContext.next();
                                }
                                else
                                {
                                    routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                            .setStatusCode(SC_FORBIDDEN).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(API_ACCESS_FAILED_NOT_AUTHORIZED, getPermission(tokens.length > 1 ? tokens[0].trim() + "/" + tokens[1].trim() : tokens[0].trim(), request, routingContext.body().asJsonObject(), true))).encodePrettily());
                                }
                            }
                            else
                            {
                                routingContext.next();
                            }
                        }
                        else
                        {
                            LOGGER.warn(result.cause().getMessage());

                            routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                    .setStatusCode(SC_FORBIDDEN).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(API_ACCESS_FAILED_NOT_AUTHORIZED, result.cause().getMessage())).encodePrettily());
                        }
                    });

                }
                else
                {
                    routingContext.next();
                }
            }
            else
            {
                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .setStatusCode(SC_FORBIDDEN).end(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, API_ACCESS_FAILED_NOT_AUTHORIZED).encodePrettily());

                LOGGER.warn(String.format(API_ACCESS_FAILED_NOT_AUTHORIZED, routingContext.request().path()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

    }

    private Future<Void> validateUser(RoutingContext routingContext)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var type = routingContext.user().get(TOKEN_TYPE);

            if (type != null && type.toString().equalsIgnoreCase(TokenType.PERSONAL_ACCESS_TOKEN.getName()))
            {
                var item = PersonalAccessTokenConfigStore.getStore().getItemByValue(PERSONAL_ACCESS_TOKEN, routingContext.user().principal().getString("access_token"));

                if (item != null)
                {
                    jwtAuth.authenticate(new JsonObject().put("token", routingContext.user().principal().getString("access_token")), result ->
                    {
                        if (result.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            promise.fail(result.cause().getMessage());

                            LOGGER.warn(String.format("invalid token for user %s ", routingContext.user().get(ID)));
                        }
                    });
                }
                else
                {
                    promise.fail(String.format("invalid token for user %s ", CommonUtil.getLong(routingContext.user().get(ID))));
                }
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    private Future<Boolean> validateUser(String userName)
    {
        var promise = Promise.<Boolean>promise();

        fetchUser(userName).onComplete(result ->
        {
            if (result.succeeded())
            {
                if (USER_TYPE_LDAP.equals(result.result().getString(USER_TYPE)))
                {
                    promise.complete(Boolean.TRUE);
                }
                else
                {
                    if(result.result().getInteger(USER_LOGIN_ATTEMPTS, 0) <= MAX_LOGIN_ATTEMPTS)
                    {
                        if (result.result().getString(USER_STATUS).equalsIgnoreCase(NO))
                        {
                            promise.fail(LOGIN_FAILED_USER_DISABLED);
                        }
                        else
                        {
                            promise.complete(Boolean.TRUE);
                        }
                    }
                    else
                    {
                        promise.fail(LOGIN_FAILED_ATTEMPT_LIMIT_REACHED);
                    }
                }
            }
            else
            {
                promise.fail(LOGIN_FAILED_USER_NOT_FOUND);
            }
        });

        return promise.future();
    }

    private boolean isPasswordExpired(long userId, boolean temporaryPassword)
    {
        var valid = true;

        var user = UserConfigStore.getStore().getItem(userId);

        if (user != null && temporaryPassword && user.containsKey(USER_TEMPORARY_PASSWORD))
        {
            valid = System.currentTimeMillis() - user.getLong(USER_TEMPORARY_PASSWORD_CREATED_TIME, 0L) <= 86400000;
        }
        else if (user != null && user.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM) && user.getString(FIELD_TYPE).equalsIgnoreCase(ENTITY_TYPE_USER))
        {
            var passwordPolicy = PasswordPolicyConfigStore.getStore().getItem();

            if (passwordPolicy.getString(PasswordPolicy.PASSWORD_POLICY_EXPIRY).equalsIgnoreCase(YES))
            {
                valid = (System.currentTimeMillis() - user.getLong(USER_PASSWORD_LAST_UPDATED_TIME)) < (86400000L * passwordPolicy.getInteger(PasswordPolicy.PASSWORD_POLICY_EXPIRY_DAYS));
            }
        }

        return valid;
    }

    private String getErrorCode(String message)
    {

        try
        {

            if (message.equalsIgnoreCase(LOGIN_FAILED_INVALID_CREDENTIALS))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_FQDN_IS_EMPTY_OR_NULL))
            {
                return ErrorCodes.ERROR_CODE_INTERNAL_ERROR;
            }

            else if (message.equalsIgnoreCase(String.format(LOGIN_FAILED, LDAP_AUTH_DISABLED)))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_LDAP_AUTH_DISABLED;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_LDAP_SERVER_ERROR))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_LDAP_SERVER_ERROR;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_MISSING_OR_INVALID_LDAP_CONNECTION))
            {
                return ErrorCodes.ERROR_CODE_INTERNAL_ERROR;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_MOTADATA_USERS_GROUP_NOT_FOUND))
            {
                return ErrorCodes.ERROR_CODE_LDAP_MOTADATA_USERS_GROUP_NOT_FOUND;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_PASSWORD_MISSING) || (message.equalsIgnoreCase(LOGIN_FAILED_USER_NAME_MISSING)))
            {
                return ErrorCodes.ERROR_CODE_BAD_REQUEST;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_USER_DISABLED))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_USER_DISABLED;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_USER_NOT_FOUND))
            {
                return ErrorCodes.ERROR_CODE_USER_NOT_FOUND;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_USER_NOT_ALLOWED) || message.equalsIgnoreCase(StringEscapeUtils.escapeJavaScript(LOGIN_FAILED_USER_NOT_ALLOWED)))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_LDAP_USER_NOT_ALLOWED;
            }

            else if (message.equalsIgnoreCase(LOGIN_FAILED_USER_NOT_CONFIGURED))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_LDAP_USER_NOT_CONFIGURED;
            }

            else if (message.contains("failed to validate ldap user"))
            {
                return ErrorCodes.ERROR_CODE_LOGIN_INVALID_LDAP_USER;
            }

            else if (message.contains(RESET_PASSWORD_LDAP_USER_ERROR))
            {
                return ErrorCodes.ERROR_CODE_RESET_PASSWORD_LDAP_USER;
            }

            else if (message.contains(RESET_PASSWORD_SYSTEM_USER_ERROR))
            {
                return ErrorCodes.ERROR_CODE_RESET_PASSWORD_SUPER_ADMIN;
            }

            else if (message.contains(USER_DISABLED))
            {
                return ErrorCodes.ERROR_CODE_USER_DISABLED;
            }

            else if (message.contains(USER_NOT_FOUND))
            {
                return ErrorCodes.ERROR_CODE_USER_NOT_FOUND;
            }

            else if (message.contains(USER_EMAIL_NOT_SET))
            {
                return ErrorCodes.ERROR_CODE_USER_EMAIL_NOT_SET;
            }
            else if (message.contains(TWO_FACTOR_NOT_ENABLED))
            {
                return ErrorCodes.ERROR_CODE_MFA_NOT_ENABLED;
            }
            else if (message.contains(OTP_EXPIRED))
            {
                return ErrorCodes.ERROR_CODE_OTP_EXPIRED;
            }
            else if (message.contains(TOTP_INVALID))
            {
                return ErrorCodes.ERROR_CODE_TOTP_INVALID;
            }


        }


        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return ErrorCodes.ERROR_CODE_INTERNAL_ERROR;
    }

    private Future<JsonObject> fetchUser(String username)
    {
        var promise = Promise.<JsonObject>promise();

        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, username);

        if (user == null || user.isEmpty())
        {
            promise.fail(LOGIN_FAILED_USER_NOT_FOUND);
        }

        else
        {
            promise.complete(user);
        }

        return promise.future();
    }

    private void validateLDAPUser(JsonObject ldapServerContext, JsonObject user, String password, Promise<Boolean> validateUserPromise)
    {
        vertx.<Boolean>executeBlocking(future ->
        {
            try
            {

                if (ldapServerContext.containsKey(LDAP_AUTHENTICATION) && ldapServerContext.getString(LDAP_AUTHENTICATION).equalsIgnoreCase(YES))
                {
                    var authResult = LDAPUtil.getLDAPConnection(ldapServerContext.getString(LDAP_SERVER_PRIMARY_HOST), ldapServerContext.getString(LDAP_SERVER_SECONDARY_HOST),
                            ldapServerContext.getInteger(LDAP_SERVER_PORT), user.getString(USER_NAME), password, ldapServerContext.getString(LDAP_SERVER_FQDN), ldapServerContext.getString(LDAP_SERVER_PROTOCOL), ldapServerContext.getString(LDAP_SERVER_TYPE));

                    var ldapConnection = (LDAPConnection) authResult.get(RESULT);

                    if (ldapConnection != null)
                    {
                        var result = LDAPUtil.getLDAPUsers(ldapConnection, ldapServerContext.getString(LDAP_SERVER_FQDN), EMPTY_VALUE, ldapServerContext.containsKey(LDAP_SERVER_USER_GROUPS) ? ldapServerContext.getJsonArray(LDAP_SERVER_USER_GROUPS).getList() : null, ldapServerContext.getString(LDAP_SERVER_TYPE));

                        if (STATUS_SUCCEED.equalsIgnoreCase(result.getString(STATUS)))
                        {

                            if (result.getJsonArray(RESULT).stream().anyMatch(ldapUser -> user.getString(USER_NAME)
                                    .equalsIgnoreCase(JsonObject.mapFrom(ldapUser).getString(USER_NAME))))
                            {
                                LDAPUtil.close(ldapConnection);

                                if (user.getLong(USER_ROLE) != null && user.getJsonArray(USER_GROUPS) != null)
                                {
                                    future.complete(true);
                                }
                                else
                                {
                                    future.fail(LOGIN_FAILED_USER_NOT_CONFIGURED);
                                }
                            }
                            else
                            {
                                future.fail(StringEscapeUtils.escapeJavaScript(LOGIN_FAILED_USER_NOT_ALLOWED));
                            }
                        }
                        else
                        {
                            future.fail(result.getString(MESSAGE));
                        }
                    }
                    else
                    {
                        future.fail(String.format(LOGIN_FAILED_LDAP_SERVER_ERROR, authResult.get(MESSAGE)));
                    }
                }
                else
                {
                    future.fail(String.format(LOGIN_FAILED, LDAP_AUTH_DISABLED));
                }
            }

            catch (Exception exception)
            {
                future.fail(exception.getCause());
            }


        }, result ->
        {

            if (result.succeeded())
            {
                validateUserPromise.complete(result.result());
            }

            else
            {
                validateUserPromise.fail(result.cause());
            }
        });


    }

    private void staticResourceHandler(RoutingContext routingContext)
    {
        var route = routingContext.normalizedPath();

        var filePath = route.substring(route.indexOf("samples"));

        try (var inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath))
        {
            if (inputStream != null)
            {
                var fileName = filePath.substring(filePath.lastIndexOf('/') + 1);

                var content = IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8);

                routingContext.response().putHeader(CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + '"')
                        .putHeader(CONTENT_LENGTH, CommonUtil.getString(content.length())).setStatusCode(SC_OK).end(content);
            }

            else
            {
                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(SC_BAD_REQUEST)
                        .end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(FILE_NOT_FOUND, filePath)).encodePrettily());
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(SC_INTERNAL_SERVER_ERROR)
                    .end(new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR).put(MESSAGE, exception.getMessage()).encodePrettily());
        }
    }

    private Router eventBusHandler()
    {
        return SockJSHandler.create(vertx, new SockJSHandlerOptions().setHeartbeatInterval(60000).addDisabledTransport(Transport.XHR.name()).addDisabledTransport(Transport.JSON_P.name()).addDisabledTransport(Transport.EVENT_SOURCE.name())
                .addDisabledTransport(Transport.HTML_FILE.name())).bridge(new SockJSBridgeOptions().addInboundPermitted(new PermittedOptions().setAddress(EventBusConstants.EVENT_SERVER))
                .addOutboundPermitted(new PermittedOptions().setAddress(EventBusConstants.EVENT_UI))
                .addOutboundPermitted(new PermittedOptions().setAddressRegex(EventBusConstants.EVENT_USER_REGEX)), event ->
        {
            boolean result = true;

            try
            {
                switch (event.type())
                {
                    case SOCKET_CREATED ->
                            LOGGER.debug(String.format("socket created : %s", event.socket().remoteAddress()));

                    case SOCKET_IDLE ->
                    {
                        LOGGER.debug(String.format("socket idle : %s", event.socket().remoteAddress()));

                        result = false;
                    }


                    case SOCKET_CLOSED ->
                    {
                        LOGGER.debug(String.format("socket closed: %s", event.socket().remoteAddress()));
                        cleanup(event);
                    }

                    case REGISTER ->
                            LOGGER.debug(String.format("client registered : %s", event.socket().remoteAddress()));

                    case UNREGISTER ->
                    {
                        LOGGER.debug(String.format("client unregistered : %s", event.socket().remoteAddress()));
                        cleanup(event);
                    }

                    case SEND ->
                    { //received event from UI

                        //inject session id for one to one message ....

                        var context = event.getRawMessage().getJsonObject("body");

                        if (context != null && event.socket().webUser() != null)
                        {
                            if (context.getString(SESSION_ID) == null)
                            {
                                context.put(SESSION_ID, event.socket().webUser().principal().getString(SESSION_ID));
                            }

                            if (context.getString(USER_NAME) == null)
                            {
                                context.put(USER_NAME, event.socket().webUser().principal().getString(USER_NAME));
                            }
                        }

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("message %s received from %s", CommonUtil.removeSensitiveFields(event.getRawMessage().getJsonObject("body"), false).encodePrettily(), event.socket().remoteAddress()));

                        }
                    }

                    case RECEIVE ->
                    { // send event to UI

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("message %s sent to %s", CommonUtil.removeSensitiveFields(event.getRawMessage().getJsonObject("body"), false).encodePrettily(), event.socket().remoteAddress()));

                        }
                    }

                    default ->
                    {
                    }
                    //Do Nothing
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            finally
            {
                event.complete(result);
            }
        });
    }

    private void cleanup(BridgeEvent event)
    {
        if (event.socket().webUser() != null) //stop streaming of particular session id when socket close
        {
            ActiveUserCacheStore.getStore().deleteItem(event.socket().webUser().principal().getString(SESSION_ID));

            vertx.eventBus().send(EventBusConstants.EVENT_STREAM_STOP, new JsonObject().put(SESSION_ID, event.socket().webUser().principal().getString(SESSION_ID)));
        }
        // when user refresh page at that time socket re-connect and for old socket close event web user is null so that leads event publish issue in task manager
        else if (event.getRawMessage() != null && event.getRawMessage().getString(EventBusConstants.EVENT_ADDRESS) != null && event.getRawMessage().getString(EventBusConstants.EVENT_ADDRESS).startsWith(EventBusConstants.EVENT_USER))
        {
            vertx.eventBus().send(EventBusConstants.EVENT_STREAM_STOP, new JsonObject().put(SESSION_ID, event.getRawMessage().getString(EventBusConstants.EVENT_ADDRESS).split(EventBusConstants.EVENT_USER)[1]));
        }
    }

    private void upload(RoutingContext routingContext)
    {
        String fileUUID = null;

        String fileName = null;

        for (var fileUpload : routingContext.fileUploads())
        {
            if (GlobalConstants.OS_WINDOWS)
            {
                fileUUID = fileUpload.uploadedFileName().split(GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PATH_SEPARATOR)[1];
            }
            else
            {
                fileUUID = fileUpload.uploadedFileName().split(GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR)[1];
            }

            fileName = fileUpload.fileName();
        }

        if (fileUUID != null)
        {
            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, fileUUID).put(FILE_NAME, fileName).encodePrettily());
        }
        else
        {
            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                    .put(MESSAGE, String.format(FILE_NOT_FOUND, fileName != null ? fileName : EMPTY_VALUE))
                    .put(STATUS, GlobalConstants.STATUS_FAIL).encodePrettily());
        }

    }


    private void redirectOAuthCallback(RoutingContext routingContext)
    {
        var code = routingContext.request().params().get("code");

        var state = (JsonObject) Json.decodeValue(routingContext.request().params().get("state"));

        var sessionId = state.getString(SESSION_ID);

        var eventType = state.getString(EVENT_TYPE);

        try
        {
            if (code != null && !code.trim().isEmpty() && sessionId != null && !sessionId.trim().isEmpty())
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_OAUTH_CONTEXT_GENERATE, new JsonObject().put(OAuthUtil.CODE, code).put(SESSION_ID, sessionId), new DeliveryOptions().setSendTimeout(15 * 1000L)
                        , reply ->
                        {
                            if (reply.succeeded())
                            {
                                var event = reply.result().body();

                                EventBusConstants.publish(eventType, event.put(STATUS, STATUS_SUCCEED).put(MESSAGE, CREDENTIAL_PROFILE_TEST_SUCCEEDED));

                                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                                        .end(OAuthUtil.HTML_CLOSE_SCRIPT);
                            }
                            else
                            {

                                EventBusConstants.publish(eventType, new JsonObject().put(STATUS, STATUS_FAIL).mergeIn((JsonObject) Json.decodeValue(reply.cause().getMessage())));

                                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                                        .end(OAuthUtil.HTML_CLOSE_SCRIPT);
                            }
                        });
            }
            else
            {
                EventBusConstants.publish(eventType, new JsonObject().put(ID, sessionId).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(OAUTH_CREDENTIAL_PROFILE_TEST_FAILED, sessionId)));

                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                        .end(OAuthUtil.HTML_CLOSE_SCRIPT);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            EventBusConstants.publish(eventType, new JsonObject().put(ID, sessionId).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(CREDENTIAL_PROFILE_TEST_FAILED, exception.getMessage())));

            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML)
                    .end(OAuthUtil.HTML_CLOSE_SCRIPT);
        }
    }

    private void uploadFirmwareFile(RoutingContext routingContext)
    {
        try
        {
            if (!routingContext.fileUploads().isEmpty())
            {
                var target = new File(ConfigConstants.CONFIG_FIRMWARE_DEFAULT_DIR + PATH_SEPARATOR + routingContext.fileUploads().getFirst().fileName());

                var source = new File(routingContext.fileUploads().getFirst().uploadedFileName());

                if (!target.exists())
                {
                    FileUtils.moveFile(source, target);

                    ConfigConstants.getFirmwareFile(target).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            CommonUtil.setFilePermissions(target, GlobalConstants.RW_PERMISSION);

                            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                    .put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(RESULT, result.result().getString(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_NAME))
                                    .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILES, result.result())
                                    .encodePrettily());
                        }
                        else
                        {
                            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(MESSAGE, result.cause().getMessage())
                                    .put(STATUS, GlobalConstants.STATUS_FAIL).encodePrettily());
                        }
                    });
                }
                else
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                            .put(MESSAGE, FILE_ALREADY_EXIST)
                            .put(STATUS, GlobalConstants.STATUS_FAIL).encodePrettily());
                }

                if (source.exists())
                {
                    source.delete(); // Deleting the temporary file created by Vert.x with a random name, as the file has already been moved and saved with its original name.
                }
            }
            else
            {
                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(MESSAGE, String.format(FILE_NOT_FOUND, EMPTY_VALUE))
                        .put(STATUS, GlobalConstants.STATUS_FAIL).encodePrettily());
            }
        }
        catch (Exception exception)
        {
            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, exception.getMessage())
                    .put(STATUS, GlobalConstants.STATUS_FAIL).encodePrettily());
        }
    }

    private void uploadImage(RoutingContext routingContext)
    {
        var buffer = routingContext.body().asJsonObject().getString("file", "").replaceFirst("data:image/png;base64,", "");

        if (!buffer.isEmpty())
        {
            var fileName = UUID.randomUUID() + ".png";

            vertx.fileSystem().writeFile(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName, Buffer.buffer(Base64.getDecoder().decode(buffer)), result ->
            {

                if (result.succeeded())
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, fileName).put(FILE_NAME, fileName).encodePrettily());
                }
                else
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                            .put(MESSAGE, String.format(FILE_NOT_FOUND, CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName)).encodePrettily());
                }
            });
        }
        else
        {
            routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, STATUS_FAIL)
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                    .put(MESSAGE, EVENT_INVALID_CONTEXT).encodePrettily());
        }
    }

    private void sendLogo(RoutingContext routingContext)
    {
        try
        {
            var item = RebrandingConfigStore.getStore().getItem();

            if (Bootstrap.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + item.getString(Rebranding.REBRANDING_LOGO)))
            {
                routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(RESULT, item).encodePrettily());
            }
            else
            {
                if (Bootstrap.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + item.getString(Rebranding.REBRANDING_DARK_LOGO)))
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(RESULT, item).encodePrettily());
                }
                else
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESPONSE_CODE, SC_NOT_FOUND).put(STATUS, STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                            .put(MESSAGE, String.format(FILE_NOT_FOUND, CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + item.getString(Rebranding.REBRANDING_DARK_LOGO))).encodePrettily());
                }
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void download(RoutingContext routingContext)
    {
        var filePath = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + routingContext.request().getParam(ID); // will be uuid of uploaded file

        try
        {
            vertx.fileSystem().exists(filePath, result ->
            {
                if (result.succeeded() && result.result().equals(true))
                {
                    vertx.fileSystem().open(filePath, new OpenOptions().setCreate(false), asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var asyncFile = asyncResult.result();

                            // send filename in header, so in ui it will display filename instead of uuid

                            //for agent filename check

                            var fileName = routingContext.request().getParam(FILE_NAME) != null ? routingContext.request().getParam(FILE_NAME) : routingContext.request().getParam(ID);

                            var contentType = switch (fileName.contains(".") ? fileName.trim().split("\\.")[fileName.trim().split("\\.").length - 1] : fileName)
                            {
                                case "csv" -> "application/csv";
                                case "pdf" -> "application/pdf";
                                case "txt" -> "application/txt";
                                case "png", "gif", "jpg", "jpeg", "bmp" -> "image/*";
                                case "zip" -> "application/form-data";
                                default -> "application/octet-stream";
                            };

                            routingContext.response().putHeader("content-type", contentType)
                                    .putHeader("content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8)).setChunked(true);

                            Pump.pump(asyncFile, routingContext.response()).start();

                            asyncFile.endHandler(response ->
                            {
                                asyncFile.close();

                                routingContext.response().end();
                            });
                        }
                        else
                        {
                            routingContext.response().setStatusCode(SC_INTERNAL_SERVER_ERROR).end(new JsonObject().put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, asyncResult.cause().getLocalizedMessage()).encodePrettily());
                        }
                    });
                }
                else
                {
                    routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(SC_BAD_REQUEST).end(new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                            .put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(FILE_NOT_FOUND, routingContext.request().params().get(FILE_NAME))).encodePrettily());
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private boolean validToken(String token)
    {
        return isExpired(jwt.decode(token)).equalsIgnoreCase(EMPTY_VALUE);
    }

    private String isExpired(JsonObject payload)
    {
        var error = EMPTY_VALUE;

        try
        {
            if (payload != null)
            {
                // All dates in JWT are of type NumericDate
                // a NumericDate is: numeric value representing the number of seconds from 1970-01-01T00:00:00Z UTC until
                // the specified UTC date/time, ignoring leap seconds
                var seconds = DateTimeUtil.currentSeconds();

                if (payload.containsKey("exp") && seconds >= payload.getLong("exp"))
                {
                    error = "Expired JWT token: exp <= now";
                }

                if (error.equalsIgnoreCase(EMPTY_VALUE))
                {
                    // issue at must be in the past
                    if (payload.containsKey("iat") && payload.getLong("iat") > seconds)
                    {
                        error = "Invalid JWT token: iat > now";
                    }

                    // not before must be after now
                    if (error.equalsIgnoreCase(EMPTY_VALUE) && payload.containsKey("nbf") && payload.getLong("nbf") > seconds)
                    {
                        error = "Invalid JWT token: nbf > now";
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return error;
    }

    private String generateAccessToken(String username, long userId, String sessionId)
    {
        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug("while generating token " + UserRoleConfigStore.getStore().getItem(UserConfigStore.getStore().getItem(userId).getLong(USER_ROLE)));

        }

        //TODO for SAAS append customer id in token
        return jwtAuth.generateToken(
                new JsonObject().put(USER_NAME, username).put(GlobalConstants.ID, userId).put(SESSION_ID, sessionId).put(USER_PERMISSIONS,
                        UserRoleConfigStore.getStore().getItem(UserConfigStore.getStore().getItem(userId).getLong(USER_ROLE))
                                .getJsonArray(UserRole.USER_ROLE_CONTEXT).add("user:" + RequestType.POST.getName()).add("token:" + RequestType.POST.getName())),
                new JWTOptions()
                        .setAlgorithm(ALGO_RS512)      //access token is signed and verified using RS512 while refresh token uses RS256
                        .setSubject(API_VERSION)
                        .setIssuer(API_AUTHOR)
                        .setExpiresInMinutes(MotadataConfigUtil.devMode() ? 120 : MAX_ACCESS_TOKEN_VALIDITY_TIME_MINUTES));
    }


    private void logout(RoutingContext routingContext)
    {
        var user = routingContext.user();

        if (user != null && user.principal() != null)
        {
            var refreshToken = routingContext.body().asJsonObject().getString(AUTH_REFRESH_TOKEN);

            if (validToken(refreshToken))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_STREAM_STOP, new JsonObject().put(SESSION_ID, user.principal().getString(SESSION_ID)));

                Bootstrap.configDBService().delete(TBL_TOKEN, new JsonObject()
                                .put(FIELD_NAME, TOKEN_ID)
                                .put(VALUE, refreshToken), user.principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            AuthTokenConfigStore.getStore().deleteItem(refreshToken);

                            if (result.succeeded())
                            {
                                LOGGER.info(String.format("token removed successfully for user %s", user.principal().getString(USER_NAME)));
                            }

                            else
                            {
                                LOGGER.warn(String.format("failed to remove token for user %s", user.principal().getString(USER_NAME)));
                            }
                        });
            }

            else
            {
                LOGGER.warn("logout warning : refresh token expired...");

                Bootstrap.configDBService().delete(TBL_TOKEN,
                        new JsonObject().put(FIELD_NAME, TOKEN_ID).put(VALUE, refreshToken), routingContext.user() != null && routingContext.user().principal().getString(USER_NAME) != null ? routingContext.user().principal().getString(USER_NAME) : DEFAULT_USER, routingContext.request().remoteAddress().host(),
                        future -> routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                .setStatusCode(SC_UNAUTHORIZED).end(new JsonObject().put(MESSAGE, LOGIN_FAILED_INVALID_REFRESH_TOKEN).encodePrettily()));

                AuthTokenConfigStore.getStore().deleteItem(refreshToken);
            }

            vertx.eventBus().send(EVENT_AUDIT, new JsonObject().put(ENTITY_TABLE, TBL_USER).put(USER_NAME, user.principal().getString(USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(MESSAGE, String.format(InfoMessageConstants.AUDIT_USER_LOGOUT, user.principal().getString(USER_NAME, EMPTY_VALUE), routingContext.request().remoteAddress().host()))
                    .put(REQUEST, "logout").put(STATUS, Boolean.TRUE));
        }

        routingContext.clearUser();

        routingContext.response()
                .setStatusCode(SC_MOVED_TEMPORARILY)
                .putHeader("Location", "/")
                .end();
    }

    // #3420 improvement - Module name and role permission will append with error message ( "Client is not allowed to access API")
    private String getPermission(String endPoint, HttpServerRequest request, JsonObject params, boolean formatMessage)
    {
        var tokens = endPoint.split("/");

        //for schedulers specific check
        if (tokens.length > 1)
        {
            switch (tokens[1])
            {
                case "schedulers" -> endPoint = switch (JobScheduler.JobType.valueOfName(request.getParam(FILTER) != null ? JsonObject.mapFrom(Json.decodeValue(request.getParam(FILTER))).getString(Scheduler.SCHEDULER_JOB_TYPE) : request.getParam(Scheduler.SCHEDULER_JOB_TYPE) != null ? request.getParam(Scheduler.SCHEDULER_JOB_TYPE) : params.getString(Scheduler.SCHEDULER_JOB_TYPE)))
                {
                    case DISCOVERY -> "settings/" + "discoveries";
                    case MAINTENANCE, REDISCOVER, TOPOLOGY -> "settings/" + NMSConstants.OBJECTS;
                    case RUNBOOK -> "settings/" + "runbook-plugins";
                    case REPORT -> "visualization/" + "reports";
                    case CONFIG_BACKUP_OPERATION, CONFIG_UPGRADE_OPERATION -> "settings/" + "configurations";
                    case DATABASE_BACKUP -> "settings/" + "backup-profiles";
                    case COMPLIANCE_POLICY -> "settings/" + "compliance-policies";
                    default -> "schedulers";
                };

                case "explorers" -> endPoint = switch (ExplorerType.valueOfName(request.getParam(FILTER) != null ? JsonObject.mapFrom(Json.decodeValue(request.getParam(FILTER))).getString(Explorer.EXPLORER_TYPE) : request.getParam(Explorer.EXPLORER_TYPE) != null ? request.getParam(Explorer.EXPLORER_TYPE) : params.getString(Explorer.EXPLORER_TYPE)))
                {
                    case ExplorerType.METRIC -> "visualization/" + "metric-explorers";

                    case ExplorerType.TOPOLOGY -> "visualization/" + "topology";

                    // in future you have to add case for log, flow, trap, trace if their respected explorer comes

                    default -> "explorers";
                };
            }

            tokens = endPoint.split("/");
        }

        return formatMessage ? (StringUtils.capitalize(ENDPOINTS_BY_MODULE.getOrDefault(endPoint, EMPTY_VALUE).replace("-", " ")) + " " + StringUtils.capitalize(APIConstants.RequestType.valueOf(EXCLUDED_MODULES.containsKey(tokens.length > 1 ? tokens[1] : tokens[0]) ? REQUEST_GET.toUpperCase() : request.method().name()).getName())
        ) : (ENDPOINTS_BY_MODULE.get(endPoint) + ":" + APIConstants.RequestType.valueOf(EXCLUDED_MODULES.containsKey(tokens.length > 1 ? tokens[1] : tokens[0]) ? REQUEST_GET.toUpperCase() : request.method().name()).getName());
    }

    private void forgotPassword(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        var validationPromise = Promise.<JsonObject>promise();

        if (CommonUtil.isNotNullOrEmpty(requestBody.getString(USER_NAME)))
        {
            fetchUser(requestBody.getString(USER_NAME)).onComplete(result ->
            {
                if (result.succeeded())
                {
                    var user = result.result();

                    if (!forgotPasswordLimitExceeded(user))
                    {
                        validationPromise.fail(String.format(RESET_PASSWORD_USER_LIMIT_REACHED_ERROR,DateTimeUtil.millisToDurationWithSeconds(user.getLong(USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME, 0L)  - System.currentTimeMillis())));
                    }
                    else
                    {
                        // Update forgot password attempt count
                        // added future promise as updating attempts is neccesary before giving response back
                        updateForgotPasswordAttempts(user)
                                .onComplete(asyncResult ->
                                {
                                    if (USER_TYPE_LDAP.equalsIgnoreCase(user.getString(USER_TYPE)))
                                    {
                                        validationPromise.fail(RESET_PASSWORD_LDAP_USER_ERROR);
                                    }
                                    else if (DEFAULT_ID.equals(user.getLong(ID)))
                                    {
                                        validationPromise.fail(RESET_PASSWORD_SYSTEM_USER_ERROR);
                                    }
                                    else if (NO.equalsIgnoreCase(user.getString(USER_STATUS)))
                                    {
                                        validationPromise.fail(USER_DISABLED);
                                    }
                                    else if (user.getString(USER_EMAIL) == null && user.getString(USER_EMAIL).isBlank())
                                    {
                                        validationPromise.fail(USER_EMAIL_NOT_SET);
                                    }
                                    else if (!MailServerConfigStore.getStore().isConfigured())
                                    {
                                        validationPromise.fail(MAIL_SERVICE_FAILED);
                                    }
                                    else
                                    {
                                        validationPromise.complete(user);
                                    }
                                });
                    }
                }
                else
                {
                    validationPromise.complete(new JsonObject());
                }
            });
        }

        else
        {
            validationPromise.fail(API_REQUEST_BODY_MISSING);
        }

        validationPromise.future().onComplete(asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                try
                {
                    var user = asyncResult.result();

                    if(user != null && !user.isEmpty())
                    {
                        var temporaryPassword = RandomStringUtils.random(9, true, true);

                        // Update Temporary Password and Timestamp in DB
                        Bootstrap.configDBService().update(TBL_USER,
                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                                new JsonObject().put(USER_TEMPORARY_PASSWORD, temporaryPassword)
                                        .put(USER_TEMPORARY_PASSWORD_CREATED_TIME, System.currentTimeMillis()),
                                user.getString(USER_NAME),
                                routingContext.request().remoteAddress().host(),
                                asyncResponse ->
                                {
                                    if (asyncResponse.succeeded())
                                    {

                                        UserConfigStore.getStore().updateItems(asyncResponse.result()).onComplete(response ->
                                        {
                                            if (response.succeeded())
                                            {
                                                Notification.sendEmail(InfoMessageConstants.TEMPORARY_PASSWORD_CHANGE_SUBJECT, String.format(InfoMessageConstants.TEMPORARY_PASSWORD_CHANGE_MESSAGE, user.getString(USER_NAME), temporaryPassword), new JsonArray().add(user.getString(USER_EMAIL)));

                                                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                        .setStatusCode(SC_OK)
                                                        .end(new JsonObject()
                                                                .put(STATUS, STATUS_SUCCEED)
                                                                .put(RESPONSE_CODE, SC_OK)
                                                                .encodePrettily());
                                            }
                                            else
                                            {
                                                //Revert the changes
                                                Bootstrap.configDBService().update(TBL_USER,
                                                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                                                        new JsonObject().put(GARBAGE_FIELDS, new JsonArray().add(USER_TEMPORARY_PASSWORD).add(USER_TEMPORARY_PASSWORD_CREATED_TIME)),
                                                        user.getString(USER_NAME),
                                                        routingContext.request().remoteAddress().host(),
                                                        result ->
                                                        {
                                                            if (result.succeeded())
                                                            {
                                                                LOGGER.debug(String.format("reverting password for user id: %s done", result.result()));
                                                            }

                                                            else
                                                            {
                                                                LOGGER.error(new Exception(String.format("error occurred while reverting password for user id: %s", result.result()), result.cause()));
                                                            }

                                                            routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                                    .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                                                    .end(new JsonObject()
                                                                            .put(STATUS, STATUS_FAIL)
                                                                            .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, response.cause()))
                                                                            .encodePrettily());
                                                        });
                                            }
                                        });
                                    }

                                    else
                                    {
                                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                                .setStatusCode(SC_INTERNAL_SERVER_ERROR)
                                                .end(new JsonObject()
                                                        .put(STATUS, STATUS_FAIL)
                                                        .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResponse.cause().getMessage()))
                                                        .encodePrettily());
                                    }
                                });
                    }
                    else
                    {
                        routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                                .setStatusCode(SC_OK)
                                .end(new JsonObject()
                                        .put(STATUS, STATUS_SUCCEED)
                                        .put(RESPONSE_CODE, SC_OK)
                                        .put(MESSAGE, FORGOT_PASSWORD_SUCCEEDED)
                                        .encodePrettily());
                    }
                }

                catch (Exception exception)
                {
                    APIUtil.sendResponse(exception, routingContext);
                }
            }

            else
            {
                routingContext.response().putHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .setStatusCode(SC_BAD_REQUEST)
                        .end(new JsonObject()
                                .put(STATUS, STATUS_FAIL)
                                .put(RESPONSE_CODE, SC_BAD_REQUEST)
                                .put(ERROR_CODE, getErrorCode(asyncResult.cause().getMessage()))
                                .put(MESSAGE, asyncResult.cause().getMessage())
                                .encodePrettily());
            }
        });
    }

    /**
     * proxy handler to check server Status
     * in case of unhealthy server status, redirect to error page instead of serving application.
     *
     * @param routingContext
     */
    private void statusHandler(RoutingContext routingContext)
    {
        if (BootstrapStandalone.APP_SERVER_ONLINE.get())
        {
            routingContext.next();
        }
        else
        {
            routingContext.fail(SC_PRECONDITION_FAILED, new Exception(ErrorMessageConstants.INSUFFICIENT_DISK_SPACE_ERROR));
        }
    }

    private CorsHandler corsHandler()
    {
        var corsHandler = CorsHandler.create();

        if (MotadataConfigUtil.corsEnabled())
        {
            var port = MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name());

            var httpScheme = MotadataConfigUtil.httpsEnabled() ? "https" : "http";

            var trustedOrigins = new ArrayList<String>();

            trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getHTTPServerHost() + COLON_SEPARATOR + port);

            trustedOrigins.add(httpScheme + "://" + CommonUtil.getHostName() + COLON_SEPARATOR + port);

            trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getHost() + COLON_SEPARATOR + port);

            if (!Strings.isNullOrEmpty(MotadataConfigUtil.getVIPIPAddress()))
            {
                trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getVIPIPAddress() + COLON_SEPARATOR + port);
            }

            /*
                if port is 443 (https) or 80 (http) then the browser removes the default port from the request hence we
                need to allow the origin without port also.
             */
            if (port == 443 || port == 80)
            {
                trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getHTTPServerHost());

                trustedOrigins.add(httpScheme + "://" + CommonUtil.getHostName());

                trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getHost());

                if (!Strings.isNullOrEmpty(MotadataConfigUtil.getVIPIPAddress()))
                {
                    trustedOrigins.add(httpScheme + "://" + MotadataConfigUtil.getVIPIPAddress());
                }
            }

            // Allow user to define the trusted origins for the use case of public ip (MOTADATA-2919)
            trustedOrigins.addAll(MotadataConfigUtil.getTrustedOrigins().stream().map(Object::toString).toList());

            LOGGER.info("Trusted origins : " + trustedOrigins);

            corsHandler.addOrigins(trustedOrigins);
        }

        return corsHandler;
    }

    /**
     * Checks if a user has exceeded the forgot password request limit within the configured time window.
     * <p>
     * This method implements rate limiting for forgot password requests to prevent abuse and
     * potential security attacks. It evaluates whether a user can make another forgot password
     * request based on:
     * <ul>
     *   <li>Number of previous attempts within the time window</li>
     *   <li>Time elapsed since the last attempt</li>
     *   <li>Configured maximum attempts and time window limits</li>
     * </ul>
     * <p>
     * <strong>Rate Limiting Logic:</strong>
     * <ul>
     *   <li>If the time window has expired since the last attempt, the user can proceed</li>
     *   <li>If within the time window and under the attempt limit, the user can proceed</li>
     *   <li>If within the time window and at/over the attempt limit, the user is blocked</li>
     * </ul>
     * <p>
     * <strong>Security Benefits:</strong>
     * <ul>
     *   <li>Prevents automated attacks on the password reset functionality</li>
     *   <li>Reduces email spam from excessive reset requests</li>
     *   <li>Protects against user enumeration attacks</li>
     * </ul>
     *
     * @param user The user JsonObject containing forgot password attempt tracking data.
     *             Must include:
     *             <ul>
     *               <li>{@code USER_FORGOT_PASSWORD_ATTEMPTS} - Number of previous attempts (defaults to 0)</li>
     *               <li>{@code USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME} - Timestamp of last attempt (defaults to 0)</li>
     *             </ul>
     *
     * @return {@code true} if the user is within limits and can make another request,
     *         {@code false} if the user has exceeded the limit and should be blocked
     *
     */
    private boolean forgotPasswordLimitExceeded(JsonObject user)
    {
        var currentTime = System.currentTimeMillis();

        var attempts = user.getInteger(USER_FORGOT_PASSWORD_ATTEMPTS, 0);

        var lastAttemptTime = user.getLong(USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME, 0L);

        // If window has expired, reset attempts
        if (currentTime - lastAttemptTime > FORGOT_PASSWORD_TIME_WINDOW)
        {
            return true;
        }

        // Check if under the limit
        return attempts < MAX_FORGOT_PASSWORD_ATTEMPTS;
    }

    /**
     * Updates the forgot password attempt count and timestamp for a user in the database and cache.
     * <p>
     * This method is called whenever a user makes a forgot password request to track and
     * increment their attempt count. It performs the following operations:
     * <ul>
     *   <li>Resets attempt count to 0 if the time window has expired</li>
     *   <li>Increments the attempt count by 1</li>
     *   <li>Updates the last attempt timestamp to current time + time window</li>
     *   <li>Persists changes to the database</li>
     *   <li>Refreshes the UserConfigStore cache</li>
     * </ul>
     * <p>
     * <strong>Time Window Logic:</strong>
     * The method sets {@code USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME} to
     * {@code currentTime + FORGOT_PASSWORD_TIME_WINDOW} to establish when the
     * rate limiting window will expire.
     * <p>
     * <strong>Database Transaction:</strong>
     * Updates are performed atomically to ensure data consistency. If the database
     * update fails, the cache is not updated to maintain synchronization.
     *
     * @param user The user JsonObject containing user details. Must include:
     *             <ul>
     *               <li>{@code ID} - User's unique identifier for database updates</li>
     *               <li>{@code USER_FORGOT_PASSWORD_ATTEMPTS} - Current attempt count (optional, defaults to 0)</li>
     *               <li>{@code USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME} - Last attempt timestamp (optional, defaults to 0)</li>
     *             </ul>
     *
     * @return A {@link Future<Void>} that completes when the update operation finishes:
     *         <ul>
     *           <li><strong>Success:</strong> Both database and cache have been updated successfully</li>
     *           <li><strong>Failure:</strong> Either database update or cache refresh failed</li>
     *         </ul>
     */
    private Future<Void> updateForgotPasswordAttempts(JsonObject user)
    {
        var promise = Promise.<Void>promise();

        var lastAttemptTime = user.getLong(USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME, 0L);
        var attempts = user.getInteger(USER_FORGOT_PASSWORD_ATTEMPTS, 0);

        // If time window has expired, reset attempts
        if (lastAttemptTime - System.currentTimeMillis() <= 0)
        {
            attempts = 0;
        }

        Bootstrap.configDBService().update(TBL_USER,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, user.getLong(ID)),
                new JsonObject().put(USER_FORGOT_PASSWORD_ATTEMPTS, attempts +  1).put(USER_LAST_FORGOT_PASSWORD_ATTEMPT_TIME, System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(FORGOT_PASSWORD_TIME_WINDOW)),
                SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        UserConfigStore.getStore().updateItem(user.getLong(ID)).onComplete(asyncResult ->
                        {
                            if(asyncResult.succeeded())
                            {
                                promise.complete();

                            }
                            else
                            {
                                promise.fail(asyncResult.cause());

                                LOGGER.warn("Failed to update user login attempts in config store");
                            }

                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.warn("Failed to update user login attempts in database");
                    }
                });

        return promise.future();
    }

    /**
     * Updates the login attempt count for a user after a login attempt.
     * <p>
     * This method handles both successful and failed login attempts by either resetting
     * or incrementing the attempt counter. It's a critical component of the account
     * lockout security mechanism that helps prevent brute force attacks.
     * <p>
     * <strong>Operation Modes:</strong>
     * <ul>
     *   <li><strong>Reset Mode ({@code resetAttempt = true}):</strong>
     *       Sets attempt count to 0, typically called after successful authentication</li>
     *   <li><strong>Increment Mode ({@code resetAttempt = false}):</strong>
     *       Increases attempt count by 1, typically called after failed authentication</li>
     * </ul>
     * <p>
     * <strong>Security Integration:</strong>
     * The updated attempt count is used by {@link #validateUser(String)} to determine
     * if an account should be locked when it exceeds {MAX_LOGIN_ATTEMPTS}.
     * <p>
     * <strong>Data Persistence:</strong>
     * <ul>
     *   <li>Updates the database record immediately for persistence</li>
     *   <li>Refreshes the UserConfigStore cache for real-time access</li>
     *   <li>Uses system credentials for audit trail compliance</li>
     * </ul>
     * <p>
     * <strong>Concurrency Considerations:</strong>
     * In extremely rare cases, concurrent login attempts from the same user may cause
     * race conditions that lead to slightly inaccurate attempt counts due to non-atomic
     * read-modify-write operations. However, this does not significantly impact the
     * overall security posture or account lockout functionality under normal usage.
     * <p>
     * <p><strong>TODO:</strong> We are intentionally not enforcing strict atomicity (e.g., via
     * distributed locks or transactions) for login attempt updates, as the overhead is not
     * justified by the rarity and low impact of such race conditions. The trade-off favors
     * simplicity and performance over overengineering for an edge case.
     * <p>
     * <strong>Error Handling:</strong>
     * If either the database update or cache refresh fails, a warning is logged
     * but the operation continues to prevent authentication flow interruption.
     *
     * @param user The user JsonObject containing user details. Must include:
     *             <ul>
     *               <li>{@code ID} - User's unique identifier for database operations</li>
     *               <li>{@code USER_LOGIN_ATTEMPTS} - Current login attempt count (optional, defaults to 0)</li>
     *             </ul>
     * @param resetAttempt Boolean flag controlling the update behavior:
     *                     <ul>
     *                       <li>{@code true} - Reset attempts to 0 (successful login)</li>
     *                       <li>{@code false} - Increment attempts by 1 (failed login)</li>
     *                     </ul>
     */
    private void updateLoginAttempts(JsonObject user,boolean resetAttempt)
    {

        var attempts = user.getInteger(USER_LOGIN_ATTEMPTS, 0);

        if(resetAttempt)//as user login successfully so reset attempt count to 0
        {
            attempts = 0;
        }
        else
        {
            attempts += 1;
        }


        Bootstrap.configDBService().update(TBL_USER,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, user.getLong(ID)),
                new JsonObject().put(USER_LOGIN_ATTEMPTS, attempts), SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        UserConfigStore.getStore().updateItem(user.getLong(ID));
                    }
                    else
                    {
                        LOGGER.warn("Failed to update user login attempts");
                    }
                });
    }
}


