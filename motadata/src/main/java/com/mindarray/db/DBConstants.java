/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author                   Notes
 *     28-Feb-2025     Smit Prajapati           MOTADATA-4956: COLLECTION_TAG_RULE for Rule Based Tagging
 *     5-Mar-2025      Bharat                   MOTADATA-4740: Two factor authentication 2FA
 *     2-Apr-2025      Bharat                   MOTADATA-5637: Domain Mapping in Flow - Initial Version
 *     20-Feb-2025     Pruthviraj               MOTADATA-5285: NetRoute and NetRoute policy added
 *     22-Apr-2025     Bharat                   MOTADATA-5822: Metric Explorer Enhancements
 *     24-Jun-2025     <PERSON><PERSON>-6528 : Added TBL_EXPLORER for Common Explorers
 *     23-Jul-2025	   Nikunj Patel             MOTADATA-6236: DNSServer table constant added.
 */

package com.mindarray.db;

import com.mindarray.util.LicenseUtil;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.CURRENT_DIR;
import static com.mindarray.GlobalConstants.PATH_SEPARATOR;

public final class DBConstants
{
    public static final String TBL_BACKUP_SNAPSHOT = "tbl_config_backup_snapshot_";
    //Configuration tables
    public static final String TBL_CONFIGURATION = "tbl_config_configuration";
    public static final String TBL_CONFIG_TEMPLATE = "tbl_config_config_template";
    // table format -> version_fileType
    public static final String TBL_CONFIG_RUNNING_RESULT = "tbl_config_config_result_%s_running_config";
    public static final String TBL_CONFIG_STARTUP_RESULT = "tbl_config_config_result_%s_startup_config";
    public static final String TBL_STORAGE_PROFILE = "tbl_config_storage_profile";
    public static final String TBL_BACKUP_PROFILE = "tbl_config_backup_profile";
    public static final Set<String> BACKUP_EXCLUSION_FILES = Set.of("license-last-usage", "columns.json", "horizontal-aggregations.json", "motadata-datastore.json", "vertical-aggregations.json", "motadata.json", "agent.json", "registration.json");
    // backup file paths
    public static final String CONFIG_DB_BACKUP_PATH = CURRENT_DIR + PATH_SEPARATOR + "config-db-backups";
    public static final String DATASTORE_BACKUP_PATH = CURRENT_DIR + PATH_SEPARATOR + "datastore-backups";
    public static final String BACKUP_START_TIME = "backup.start.time";
    public static final String GARBAGE_FIELDS = "garbage.fields";
    //common const
    //config db const...
    public static final String ENTITY_TYPE_SYSTEM = "0";
    public static final String ENTITY_TYPE_USER = "1";
    public static final String FIELD_NAME = "field";
    public static final String FIELD_TYPE = "_type";
    public static final String ENTRIES = "entries";
    public static final String TYPE = "type";
    public static final String RECORDS = "records";
    public static final String INLINE = "inline";
    public static final String FILE = "file";
    public static final String DIRECTORY = "directory";
    public static final String SCRIPT = "script";
    public static final String ENCODED = "encoded";
    //tables config db const...
    public static final String TBL_DNS_RECORD = "tbl_config_dns_record";
    public static final String TBL_SYSTEM = "tbl_config_system";
    public static final String TBL_PLUGIN_ID = "tbl_config_plugin_id";
    public static final String TBL_MAIL_SERVER = "tbl_config_mail_server";
    public static final String TBL_SMS_GATEWAY = "tbl_config_sms_gateway";
    public static final String TBL_REBRANDING = "tbl_config_rebranding";
    public static final String TBL_USER = "tbl_config_user";
    public static final String TBL_TOKEN = "tbl_config_token";
    public static final String TBL_PERSONAL_ACCESS_TOKEN = "tbl_config_personal_access_token";
    public static final String TBL_INTEGRATION_PROFILE = "tbl_config_integration_profile";
    public static final String TBL_USER_ROLE = "tbl_config_user_role";
    public static final String TBL_GROUP = "tbl_config_group";
    public static final String TBL_PASSWORD_POLICY = "tbl_config_password_policy";
    public static final String TBL_REMOTE_EVENT_PROCESSOR = "tbl_config_remote_event_processor";
    public static final String TBL_LDAP_SERVER = "tbl_config_ldap_server";
    public static final String TBL_CREDENTIAL_PROFILE = "tbl_config_credential_profile";
    public static final String TBL_INTEGRATION = "tbl_config_integration";
    public static final String TBL_DISCOVERY = "tbl_config_discovery";
    public static final String TBL_DISCOVERY_RESULT = "tbl_config_discovery_result_";
    public static final String TBL_OBJECT = "tbl_config_object";
    public static final String TBL_SNMP_DEVICE_CATALOG = "tbl_config_snmp_device_catalog";
    public static final String TBL_SNMP_OID_GROUP = "tbl_config_oid_group";
    public static final String TBL_AGENT = "tbl_config_agent";
    public static final String TBL_BUSINESS_HOUR = "tbl_config_business_hour";
    public static final String TBL_SYSTEM_PROCESS = "tbl_config_system_process";
    public static final String TBL_SYSTEM_SERVICE = "tbl_config_system_service";
    public static final String TBL_METRIC = "tbl_config_metric";
    public static final String TBL_EXPLORER = "tbl_config_explorer";
    public static final String TBL_SCHEDULER = "tbl_config_scheduler";
    public static final String TBL_METRIC_PLUGIN = "tbl_config_metric_plugin";
    public static final String TBL_RUNBOOK_PLUGIN = "tbl_config_runbook_plugin";
    public static final String TBL_SNMP_TRAP_PROFILE = "tbl_config_snmp_trap_profile";
    public static final String TBL_SNMP_TRAP_FORWARDER = "tbl_config_snmp_trap_forwarder";
    public static final String TBL_SNMP_TRAP_LISTENER = "tbl_config_snmp_trap_listener";
    public static final String TBL_CUSTOM_MONITORING_FIELD = "tbl_config_custom_monitoring_field";
    public static final String TBL_SYSTEM_FILE = "tbl_config_system_file";
    public static final String TBL_PROXY_SERVER = "tbl_config_proxy_server";
    public static final String TBL_TWO_FACTOR_AUTHENTICATION = "tbl_config_two_factor_authentication";
    public static final String TBL_TOPOLOGY_PLUGIN = "tbl_config_topology_plugin";
    public static final String TBL_MAC_SCANNER = "tbl_config_mac_scanner";
    public static final String TBL_DEPENDENCY = "tbl_config_dependency";
    public static final String TBL_ARTIFACT = "tbl_config_artifact";
    public static final String TBL_TAG_RULE = "tbl_config_tag_rule";
    //dependency const
    public static final String TBL_CORRELATION = "tbl_config_correlation";
    public static final String TBL_DEPENDENCY_MAPPER = "tbl_config_dependency_mapper";
    public static final String TBL_DASHBOARD = "tbl_config_dashboard";
    public static final String TBL_WIDGET = "tbl_config_widget";
    public static final String TBL_TEMPLATE = "tbl_config_template";
    //widget dashboard tables
    public static final String TBL_LOG_PARSER = "tbl_config_log_parser";
    public static final String TBL_LOG_COLLECTOR = "tbl_config_log_collector";
    public static final String TBL_LOG_PARSER_PLUGIN = "tbl_config_log_parser_plugin";
    public static final String TBL_EVENT_SOURCE = "tbl_config_event_source";
    public static final String TBL_TAG = "tbl_config_tag";
    public static final String TBL_LOG_FORWARDER = "tbl_config_log_forwarder";
    public static final String TBL_REPORT = "tbl_config_report";
    //log parser tables
    public static final String TBL_FLOW_SAMPLING_RATE = "tbl_config_flow_sampling_rate";
    public static final String TBL_FLOW_IP_GROUP = "tbl_config_flow_ip_group";
    public static final String TBL_FLOW_SETTINGS = "tbl_config_flow_setting";
    public static final String TBL_APPLICATION_MAPPER = "tbl_config_application_mapper";
    public static final String TBL_PROTOCOL_MAPPER = "tbl_config_protocol_mapper";
    public static final String TBL_FLOW_IP_MAPPER = "tbl_config_flow_ip_mapper";
    public static final String TBL_FLOW_AS_MAPPER = "tbl_config_flow_as_mapper";
    public static final String TBL_FLOW_DOMAIN_MAPPER = "tbl_config_flow_domain_mapper";
    public static final String TBL_FLOW_GEOLOCATION_MAPPER = "tbl_config_flow_geolocation_mapper";
    //flow tables
    public static final String TBL_METRIC_POLICY = "tbl_config_metric_policy";
    public static final String TBL_EVENT_POLICY = "tbl_config_event_policy";
    public static final String TBL_DATA_RETENTION_POLICY = "tbl_config_data_retention_policy";
    public static final String TBL_SINGLE_SIGN_ON = "tbl_config_single_sign_on";
    //backup constant tables
    public static final String BACKUP_END_TIME = "backup.end.time";
    public static final String BACKUP_FILE = "backup.file";
    public static final int MAX_LOCAL_BACKUP_COPIES = 7;
    // Compliance tables
    public static final String TBL_COMPLIANCE_RULE = "tbl_config_compliance_rule";
    public static final String TBL_COMPLIANCE_BENCHMARK = "tbl_config_compliance_benchmark";
    public static final String TBL_COMPLIANCE_POLICY = "tbl_config_compliance_policy";
    public static final String TBL_COMPLIANCE_WEIGHTED_CALCULATION = "tbl_config_compliance_weighted_calculation";
    // NetRoute tables
    public static final String TBL_NETROUTE = "tbl_config_netroute";
    public static final String TBL_NETROUTE_POLICY = "tbl_config_netroute_policy";
    public static final String DB_QUERY = "db.query";
    // DNS Server
    public static final String TBL_DNS_SERVER_PROFILE = "tbl_config_dns_server_profile";


    //HA Const
    public static final String DB_RECORDS = "db.records";
    //PG Tables for Compliance
    public static final String COMPLIANCE_DB = "Compliance DB";
    public static final String TABLE_COMPLIANCE_TRAIL = "tbl_compliance_trail";
    public static final String TABLE_COMPLIANCE_STATS_ENTITY = "tbl_compliance_stats_entity";
    public static final String TABLE_COMPLIANCE_STATS_POLICY = "tbl_compliance_stats_policy";
    public static final String QUERY_COMPLIANCE_TRAIL_TABLE = "SELECT compliance_policy_id, object_id, compliance_rule_id, current_scan_timestamp, current_scan_status FROM tbl_compliance_trail";
    public static final String QUERY_COMPLIANCE_STATS_POLICY_TABLE = "SELECT compliance_policy_id, compliance_percentage FROM tbl_compliance_stats_policy";
    public static final String QUERY = "query";
    public static final String COLUMNS = "columns";
    public static final String COLUMN_ALIASES = "column.aliases";
    public static final String SELECT = "SELECT";
    public static final String WHERE = "WHERE";
    public static final String FROM = "FROM";
    public static final String GROUP_BY = "GROUP BY";
    public static final String AS = "AS";
    public static final String DELETE = "DELETE";
    public static final String INSERT = "INSERT";
    public static final String LIMIT = "LIMIT";
    public static final String ORDER_BY = "ORDER BY";
    public static final String OR = "OR";
    public static final String UPDATE = "UPDATE";
    public static final String SET = "SET";
    public static final String TRANSFORM = "transform";
    public static final String TABLE_QUERY_COMPLIANCE_STATS_POLICY = "CREATE TABLE IF NOT EXISTS tbl_compliance_stats_policy (compliance_policy_id BIGINT PRIMARY KEY, compliance_percentage INTEGER CHECK (compliance_percentage BETWEEN 0 AND 100), vulnerable INTEGER DEFAULT 0, poor INTEGER DEFAULT 0, moderate INTEGER DEFAULT 0, secure INTEGER DEFAULT 0, last_scan_timestamp BIGINT NOT NULL)";
    public static final String TABLE_QUERY_COMPLIANCE_TRAIL = "CREATE TABLE IF NOT EXISTS tbl_compliance_trail (id BIGSERIAL PRIMARY KEY, compliance_policy_id BIGINT NOT NULL, compliance_benchmark_id BIGINT NOT NULL, compliance_rule_id BIGINT NOT NULL, object_id INTEGER NOT NULL, compliance_rule_severity VARCHAR NOT NULL, current_scan_status INTEGER NOT NULL, last_scan_status INTEGER NOT NULL, current_scan_timestamp BIGINT NOT NULL, last_scan_timestamp BIGINT NOT NULL)";
    public static final String TABLE_QUERY_COMPLIANCE_STATS_ENTITY = "CREATE TABLE IF NOT EXISTS tbl_compliance_stats_entity (id BIGSERIAL PRIMARY KEY, compliance_policy_id BIGINT NOT NULL, object_id INTEGER NOT NULL, compliance_percentage INTEGER NOT NULL, severity VARCHAR, last_scan_status INTEGER NOT NULL, last_scan_timestamp BIGINT NOT NULL, scanned_rule INTEGER NOT NULL, message VARCHAR NULL)";
    public static final String INDEX_QUERY_COMPLIANCE_POLICY_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_policy_id ON %s (compliance_policy_id)";
    public static final String INDEX_QUERY_OBJECT_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_object_id ON %s (object_id)";
    public static final String INDEX_QUERY_RULE_ID = "CREATE INDEX IF NOT EXISTS idx_compliance_rule_id ON tbl_compliance_trail (compliance_rule_id)";

    public static List<String> schemas;

    private DBConstants()
    {
    }

    public static List<String> getEntitySchemas()
    {
        if (schemas == null)
        {
            var list = new ArrayList<String>();

            list.add("dependencies.json");
            list.add("flow-ip-groups.json");
            list.add("protocol-mappers.json");
            list.add("policy-first-trigger-ticks.json");
            list.add("event-sources.json");
            list.add("data-retention-policy.json");
            list.add("system-services.json");
            list.add("dependency-mappers.json");
            list.add("backup-profiles.json");
            list.add("tags.json");
            list.add("netroutes.json");
            list.add("snmp-trap-forwarders.json");
            list.add("agents.json");
            list.add("snmp-trap-profiles.json");
            list.add("artifact.json");
            list.add("flow-ip-mappers.json");
            list.add("widgets.json");
            list.add("password-policy.json");
            list.add("integrations.json");
            list.add("application-mappers.json");
            list.add("explorers.json");
            list.add("column-mapper.json");
            list.add("remote-event-processors.json");
            list.add("compliance-policies.json");
            list.add("users.json");
            list.add("config-templates.json");
            list.add("flow-as-mappers.json");
            list.add("custom-monitoring-fields.json");
            list.add("metric-policies.json");
            list.add("mail-server-configuration.json");
            list.add("tag-rules.json");
            list.add("metric-plugins.json");
            list.add("compliance-rules.json");
            list.add("snmp-trap-listener-configuration.json");
            list.add("dashboards.json");
            list.add("system-processes.json");
            list.add("flow-settings.json");
            list.add("runbook-plugins.json");
            list.add("sms-gateway-configuration.json");
            list.add("topology-plugins.json");
            list.add("personal-access-tokens.json");
            list.add("rebranding.json");
            list.add("business-hours.json");
            list.add("user-roles.json");
            list.add("auth-token.json");
            list.add("plugin-id.json");
            list.add("single-sign-on.json");
            list.add("license.json");
            list.add("netroute-policies.json");
            list.add("ldap-servers.json");
            list.add("correlations.json");
            list.add("proxy-server.json");
            list.add("compliance-weighted-calculations.json");
            list.add("log-parser-plugins.json");
            list.add("integration-profiles.json");
            list.add("mac-scanners.json");
            list.add("objects.json");
            list.add("log-forwarders.json");
            list.add("storage-profiles.json");
            list.add("flow-domain-mappers.json");
            list.add("flow-geolocation-mappers.json");
            list.add("log-collectors.json");
            list.add("system-files.json");
            list.add("groups.json");
            list.add("credential-profiles.json");
            list.add("snmp-device-catalogs.json");
            list.add("discoveries.json");
            list.add("configurations.json");
            list.add("dns-record.json");
            list.add("schedulers.json");
            list.add("two-factor-authentication.json");
            list.add("compliance-benchmarks.json");
            list.add("reports.json");
            list.add("flow-sampling-rates.json");
            list.add("templates.json");
            list.add("metrics.json");
            list.add("snmp-oid-groups.json");
            list.add("dns-server-profiles.json");

            if (LicenseUtil.LICENSE_EDITION.get() != LicenseUtil.LicenseEdition.HYBRID_LITE && LicenseUtil.LICENSE_EDITION.get() != LicenseUtil.LicenseEdition.ESSENTIAL)
            {
                list.add("log-parsers.json");
                list.add("event-policies.json");
            }

            schemas = Collections.unmodifiableList(list);
        }

        return schemas;
    }

    public static void where(StringBuilder filter, StringBuilder query)
    {
        query.append(" ").append(WHERE).append(" ").append(filter);
    }

    public static void select(Map<String, JsonArray> columns, StringBuilder query)
    {
        query.append(SELECT).append(" ");

        for (var i = 0; i < columns.get(COLUMNS).size(); i++)
        {
            query.append(columns.get(COLUMNS).getString(i).replace(".", "_")).append(" ");

            if (columns.containsKey(COLUMN_ALIASES) && !columns.get(COLUMN_ALIASES).getString(i).isEmpty())
            {
                query.append(" ").append(AS).append(" ").append(columns.get(COLUMN_ALIASES).getString(i)).append(" ");
            }

            if (i < columns.get(COLUMNS).size() - 1)
            {
                query.append(", ");
            }
        }
    }

    // Add OR WHERE condition (subsequent conditions)
    public static StringBuilder orWhere(String condition, Object value, StringBuilder query)
    {
        return query.append(" ").append(OR).append(" ").append(condition);
    }

    public static void from(String table, StringBuilder query)
    {
        query.append(" ").append(FROM).append(" ").append(table);
    }

    public static StringBuilder orderBy(String column, boolean ascending, StringBuilder query)
    {

        return query.append(" ").append(ORDER_BY).append(" ").append(column).append(ascending ? " ASC" : " DESC");
    }

    public static void groupBy(JsonArray columns, StringBuilder query)
    {
        query.append(" ").append(GROUP_BY).append(" ");

        for (var i = 0; i < columns.size(); i++)
        {
            query.append(columns.getString(i).replace(".", "_"));

            if (i < columns.size() - 1)
            {
                query.append(", ");
            }
        }

    }

    public static StringBuilder AS(String alias, StringBuilder query)
    {

        return query.append(" ").append(AS).append(" ").append(alias);
    }

    public static StringBuilder limit(int limit, StringBuilder query)
    {

        return query.append(" ").append(LIMIT).append(" ").append(limit);
    }

    public static StringBuilder update(String table, StringBuilder query)
    {

        return query.append(UPDATE).append(" ").append(table);
    }

    public static StringBuilder set(String column, Object value, Integer Count, StringBuilder query)
    {
        /*
         todo: code required to add for parameter..
         */
        if (query.indexOf("SET") == -1)
        {
            query.append(" SET ");
        }
        else
        {
            query.append(", ");
        }

        query.append(column).append(" = $").append(Count);

        return query;
    }

    /**
     * Generates a dynamic Upsert query for a single ID and column.
     *
     * @param table          The name of the table.
     * @param columns        A HashMap containing the column name and its value for the clause.
     * @param conflictColumn name of the conflict columns.
     * @param query          A StringBuilder object to build the query.
     * @return The completed query as a StringBuilder.
     */
    public static StringBuilder upsert(String table, Map<String, Object> columns, String conflictColumn, StringBuilder query)
    {
        query.append("INSERT INTO ").append(table).append(" (");

        // Add column names and placeholders

        query.append(String.join(", ", columns.keySet())).append(") VALUES (").append(columns.keySet().stream().map(k -> "$").collect(Collectors.joining(", "))).append(")");

        // Add ON CONFLICT clause
        query.append(" ON CONFLICT (").append(conflictColumn).append(") DO UPDATE SET ");

        // Build the update clause
        query.append(columns.keySet().stream()
                .map(column -> column + " = excluded." + column)
                .collect(Collectors.joining(", ")));

        // Add values to parameters
        //parameters.addAll(values.values());

        return query;
    }

    /**
     * Generates a dynamic DELETE query for a single ID and column.
     *
     * @param table     The name of the table.
     * @param condition A JsonObject containing the column name and its value for the WHERE clause.
     * @param query     A StringBuilder object to build the query.
     */
    public static void delete(String table, JsonObject condition, StringBuilder query)
    {
        query.append(DELETE).append(" ").append(FROM).append(" ").append(table).append(" ").append(WHERE).append(" ");

        for (var result : condition.getMap().entrySet())
        {
            query.append(result.getKey()).append(" = '").append(result.getValue()).append("'");
        }

    }
}
