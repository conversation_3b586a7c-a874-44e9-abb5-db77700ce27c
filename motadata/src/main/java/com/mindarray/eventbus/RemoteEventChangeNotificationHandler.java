/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  18-Mar-2025     Smit Prajapati      MOTADATA-5431: Module Level Logging
 *  26-Mar-2025     Smit Prajapati      MOTADATA-5435: Cleanup job for Flow back-pressure mechanism.
 *  2-Apr-2025      Bharat              MOTADATA-5637: Domain Mapping in Flow
 */
package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.core.Core;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.LogUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.FILENAME;
import static com.mindarray.api.LogParserPlugin.LOG_PARSER_PLUGIN_NAME;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_LOG_PARSER_PLUGIN;

/**
 * The RemoteEventChangeNotificationHandler class processes change notifications from remote components.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Handling configuration change notifications from remote systems</li>
 *   <li>Synchronizing configuration data across distributed components</li>
 *   <li>Initiating and controlling discovery, topology, and plugin engine operations</li>
 *   <li>Managing configuration initialization and updates</li>
 *   <li>Controlling logging levels across the system</li>
 *   <li>Performing maintenance operations like cache cleanup</li>
 * </ul>
 * <p>
 * The handler maintains a mapping of configuration stores by table name, allowing it to
 * route configuration changes to the appropriate stores. It responds to various types of
 * change notifications including:
 * <ul>
 *   <li>Discovery operations (run, complete, abort)</li>
 *   <li>Topology operations (run, complete, stop)</li>
 *   <li>Plugin engine operations</li>
 *   <li>Configuration initialization and changes</li>
 *   <li>Logger level changes</li>
 *   <li>Flow cache cleanup</li>
 * </ul>
 * <p>
 * For each type of change notification, the handler performs the necessary actions to
 * ensure that the system configuration remains consistent across all components and that
 * operations are properly coordinated.
 * <p>
 * Example usage:
 * <pre>
 * // Deploy the RemoteEventChangeNotificationHandler
 * vertx.deployVerticle(new RemoteEventChangeNotificationHandler());
 *
 * // Send a change notification event
 * JsonObject event = new JsonObject()
 *     .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.RUN_DISCOVERY.name())
 *     .put(ID, discoveryId);
 *
 * vertx.eventBus().send(EVENT_CHANGE_NOTIFICATION, event);
 * </pre>
 */
public class RemoteEventChangeNotificationHandler extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(RemoteEventChangeNotificationHandler.class, MOTADATA_EVENT_BUS, "Remote Event Change Notification Handler");
    private static final DateTimeFormatter FILE_TIMESTAMP_FORMAT = DateTimeFormat.forPattern("yyyyMMddHHmm");
    private static final Map<String, AbstractConfigStore> STORES_BY_TABLE =
            Map.ofEntries(Map.entry(TBL_DISCOVERY, DiscoveryConfigStore.getStore()),
                    Map.entry(TBL_DEPENDENCY_MAPPER, DependencyMapperConfigStore.getStore()),
                    Map.entry(TBL_APPLICATION_MAPPER, ApplicationMapperConfigStore.getStore()),
                    Map.entry(TBL_METRIC, MetricConfigStore.getStore()),
                    Map.entry(TBL_METRIC_PLUGIN, MetricPluginConfigStore.getStore()),
                    Map.entry(TBL_RUNBOOK_PLUGIN, RunbookPluginConfigStore.getStore()),
                    Map.entry(TBL_SNMP_OID_GROUP, SNMPOIDGroupConfigStore.getStore()),
                    Map.entry(TBL_SCHEDULER, SchedulerConfigStore.getStore()),
                    Map.entry(TBL_SYSTEM_PROCESS, SystemProcessConfigStore.getStore()),
                    Map.entry(TBL_SYSTEM_FILE, SystemFileConfigStore.getStore()),
                    Map.entry(TBL_SYSTEM_SERVICE, SystemServiceConfigStore.getStore()),
                    Map.entry(TBL_TOPOLOGY_PLUGIN, TopologyPluginConfigStore.getStore()),
                    Map.entry(TBL_OBJECT, ObjectConfigStore.getStore()),
                    Map.entry(TBL_BUSINESS_HOUR, BusinessHourConfigStore.getStore()),
                    Map.entry(TBL_CREDENTIAL_PROFILE, CredentialProfileConfigStore.getStore()),
                    Map.entry(TBL_AGENT, AgentConfigStore.getStore()),
                    Map.entry(TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore()),
                    Map.entry(TBL_SNMP_DEVICE_CATALOG, SNMPDeviceCatalogConfigStore.getStore()),
                    Map.entry(TBL_LOG_PARSER, LogParserConfigStore.getStore()),
                    Map.entry(TBL_LOG_PARSER_PLUGIN, LogParserPluginConfigStore.getStore()),
                    Map.entry(TBL_CONFIG_TEMPLATE, ConfigTemplateConfigStore.getStore()),
                    Map.entry(TBL_CONFIGURATION, ConfigurationConfigStore.getStore()),
                    Map.entry(TBL_STORAGE_PROFILE, StorageProfileConfigStore.getStore()),
                    Map.entry(TBL_FLOW_SETTINGS, FlowSettingsConfigStore.getStore()),
                    Map.entry(TBL_EVENT_SOURCE, EventSourceConfigStore.getStore()),
                    Map.entry(TBL_PROTOCOL_MAPPER, ProtocolMapperConfigStore.getStore()),
                    Map.entry(TBL_FLOW_SAMPLING_RATE, FlowSamplingRateConfigStore.getStore()),
                    Map.entry(TBL_FLOW_AS_MAPPER, FlowASMapperConfigStore.getStore()),
                    Map.entry(TBL_FLOW_DOMAIN_MAPPER, FlowDomainMapperConfigStore.getStore()),
                    Map.entry(TBL_FLOW_GEOLOCATION_MAPPER, FlowGeolocationMapperConfigStore.getStore())
            );
    private long timer = 0L;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
            {
                case RUN_DISCOVERY -> vertx.eventBus().send(EventBusConstants.EVENT_DISCOVERY_RUN, event);

                case COMPLETE_DISCOVERY ->
                        vertx.eventBus().send(EventBusConstants.EVENT_DISCOVERY_COMPLETE, event.getLong(GlobalConstants.ID));

                case ABORT_DISCOVERY ->
                        vertx.eventBus().send(EventBusConstants.EVENT_DISCOVERY_ABORT, event.getLong(GlobalConstants.ID));

                case STOP_REDISCOVER_JOB -> vertx.eventBus().send(EventBusConstants.EVENT_REDISCOVER_STOP, event);

                case ABORT_PLUGIN_ENGINE_REQUEST ->
                        vertx.eventBus().send(EventBusConstants.EVENT_PLUGIN_ENGINE_ABORT, event);

                case RUN_TOPOLOGY -> vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_RUN, event);

                case COMPLETE_TOPOLOGY -> vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_COMPLETE, event);

                case STOP_TOPOLOGY -> vertx.eventBus().send(EventBusConstants.EVENT_TOPOLOGY_STOP, event);

                case CONFIG_INIT ->
                {


                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("Config init event received...");
                    }

                    for (var entry : event.getJsonObject(GlobalConstants.RESULT))
                    {
                        var store = STORES_BY_TABLE.get(entry.getKey());

                        if (store != null)
                        {
                            store.clear();

                            for (var object : (JsonArray) entry.getValue())
                            {
                                var item = (JsonObject) object;

                                store.addItem(item.getLong(GlobalConstants.ID), item);
                            }

                        }
                    }

                    if (event.containsKey(LICENSE) && event.getJsonObject(LICENSE) != null)
                    {
                        LicenseUtil.load(event.getJsonObject(LICENSE));

                        Bootstrap.vertx().eventBus().publish(EVENT_CONFIG_INIT, new JsonObject().put(STATUS, STATUS_SUCCEED)); //After config init, deploy all the required verticals.
                    }
                    else
                    {
                        LOGGER.warn("licence details not received...");
                    }

                }

                case CONFIG_CHANGE ->
                {
                    var table = event.getString(APIConstants.ENTITY_TABLE);

                    var request = event.getString(APIConstants.REQUEST);

                    if (CommonUtil.isNotNullOrEmpty(table) && CommonUtil.isNotNullOrEmpty(request) && STORES_BY_TABLE.get(table) != null)
                    {
                        var store = STORES_BY_TABLE.get(table);

                        if (request.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || request.equalsIgnoreCase(APIConstants.REQUEST_UPDATE))
                        {
                            store.addItem(event.getLong(GlobalConstants.ID), event.getJsonObject(GlobalConstants.RESULT));
                        }
                        else if (request.equalsIgnoreCase(APIConstants.REQUEST_DELETE))
                        {
                            store.deleteItem(event.getLong(GlobalConstants.ID));
                        }

                        if (APIConstants.REQUEST_CREATE.equalsIgnoreCase(request) && TBL_LOG_PARSER_PLUGIN.equalsIgnoreCase(table))
                        {
                            event.mergeIn(event.getJsonObject(RESULT));

                            event.remove(RESULT);

                            var fileName = event.getString(LOG_PARSER_PLUGIN_NAME).replace("-", "").replace(" ", "");

                            Core.<JsonObject>request(EVENT_CHANGE_LOCAL_NOTIFICATION, event.put(FILENAME, fileName).put(EVENT_REPLY, YES).put(CHANGE_NOTIFICATION_TYPE, ADD_LOG_PARSER_PLUGIN)).onComplete(reply ->
                            {

                                if (reply.succeeded())
                                {
                                    var item = reply.result();

                                    if (item.containsKey(STATUS) && STATUS_SUCCEED.equalsIgnoreCase(item.getString(STATUS)))
                                    {
                                        Bootstrap.vertx().eventBus().publish(EVENT_CONFIG_CHANGE, event);
                                    }
                                    else if (item.containsKey(ERROR) && ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD.equalsIgnoreCase(item.getString(ERROR)))
                                    {
                                        LOGGER.warn(ErrorMessageConstants.LOG_PARSER_INVALID_PLUGIN_METHOD);
                                    }
                                    else
                                    {
                                        LOGGER.warn(ErrorMessageConstants.TIMED_OUT);
                                    }
                                }
                                else
                                {
                                    LOGGER.warn(ErrorMessageConstants.TIMED_OUT);
                                }
                            });
                        }
                        else
                        {
                            Bootstrap.vertx().eventBus().publish(EVENT_CONFIG_CHANGE, event);
                        }
                    }
                }

                case DELETE_REMOTE_POLLER ->
                {
                    //Todo: implement shutdown code...
                }
                case LOGGER_LEVEL_CHANGE ->
                {
                    LOGGER.info(String.format("Changing log-level: %s to %s", LOG_LEVELS.get(CommonUtil.getLogLevel()), LOG_LEVELS.get(event.getInteger(SYSTEM_LOG_LEVEL))));

                    if (LOG_LEVEL_INFO != event.getInteger(SYSTEM_LOG_LEVEL))
                    {
                        LoggerCacheStore.getStore().update(event.getJsonArray(SYSTEM_LOG_MODULES));
                    }

                    LogUtil.setLogLevel(event.getInteger(SYSTEM_LOG_LEVEL));

                    vertx.cancelTimer(timer);

                    timer = vertx.setTimer(TimeUnit.SECONDS.toMillis(CommonUtil.getLong(event.getString(LOG_LEVEL_RESET_TIMER_SECONDS))), timer ->
                    {
                        LOGGER.info(String.format("Changing log-level: %s to %s", GlobalConstants.LOG_LEVELS.get(CommonUtil.getLogLevel()), GlobalConstants.LOG_LEVELS.get(GlobalConstants.LOG_LEVEL_INFO)));

                        LogUtil.setLogLevel(GlobalConstants.LOG_LEVEL_INFO);
                    });

                }

                case CLEANUP_FLOW_CACHE -> Bootstrap.vertx().fileSystem().readDir(FLOW_CACHE_DIRECTORY_PATH).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        var items = new JsonArray();

                        for (var item : result.result())
                        {
                            try
                            {
                                // Extract timestamp from filename (last 12 characters: yyyyMMddHHmm)
                                var time = FILE_TIMESTAMP_FORMAT.parseDateTime(item.substring(item.length() - 12));

                                // check is file time is older than current time, if yes then we will delete that old file
                                if (time.isBefore(DateTime.now().minusMinutes(60)))
                                {
                                    items.add(item);

                                    // Delete files older than aggregationTimer time
                                    Bootstrap.vertx().fileSystem().delete(item).onComplete(asyncResult ->
                                    {

                                        if (asyncResult.succeeded())
                                        {
                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug("Deleted old file: " + item);

                                            }
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());
                                        }
                                    });
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }

                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_FLOW_CACHE_CLEANUP, items);
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });

                default ->
                {
                    // do nothing
                }

            }
        });

        promise.complete();

    }
}
