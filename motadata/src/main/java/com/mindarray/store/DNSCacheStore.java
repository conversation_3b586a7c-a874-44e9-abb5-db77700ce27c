/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  23-Jul-2025	    Nikun<PERSON> Patel		     MOTADATA-6236: Added helper methods for DNS lookup.
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.DEFAULT_USER;
import static com.mindarray.GlobalConstants.SYSTEM_REMOTE_ADDRESS;

public class DNSCacheStore extends AbstractCacheStore
{

    private static final Logger LOGGER = new Logger(DNSCacheStore.class, GlobalConstants.MOTADATA_STORE, "DNS Cache Store");

    private static final DNSCacheStore STORE = new DNSCacheStore();

    private final Map<String, String> records = new ConcurrentHashMap<>();

    private final Set<String> ips = new HashSet<>(); // unique ips from flow

    private final int UNIQUE_IP_LIMIT = MotadataConfigUtil.getDNSCacheUniqueIPLimit();

    private DNSCacheStore()
    {
    }

    public static DNSCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");

        }

        else
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_DNS_RECORD, future ->
            {

                if (future.succeeded())
                {
                    for (var index = 0; index < future.result().size(); index++)
                    {
                        var item = future.result().getJsonObject(index);

                        records.put(item.getString("ip"), item.getString("host"));

                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("item added %s into the store", item.encodePrettily()));
                        }
                    }
                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }

                else
                {
                    LOGGER.error(future.cause());

                    promise.fail(future.cause());
                }

            });
        }

        return promise.future();
    }

    public Future<String> lookup(String host)
    {
        var promise = Promise.<String>promise();

        if (records.containsValue(host))
        {
            promise.complete(records.entrySet()
                    .stream()
                    .filter(entry -> host.equalsIgnoreCase(entry.getValue()))
                    .map(Map.Entry::getKey)
                    .findFirst().get());
        }

        else
        {
            Bootstrap.vertx().eventBus().<String>request(EventBusConstants.EVENT_DNS_LOOKUP, host, reply ->
            {

                if (reply.succeeded())
                {
                    if (reply.result().body() != null)
                    {
                        promise.complete(reply.result().body());

                        records.put(reply.result().body(), host);
                    }

                    else
                    {
                        promise.fail("No record found");
                    }
                }

                else
                {
                    promise.fail(reply.cause().getMessage());
                }

            });
        }


        return promise.future();
    }

    public Future<String> reverseLookup(String ip)
    {
        var promise = Promise.<String>promise();

        if (records.containsKey(ip))
        {
            promise.complete(records.get(ip));
        }

        else
        {
            Bootstrap.vertx().eventBus().<String>request(EventBusConstants.EVENT_DNS_REVERSE_LOOKUP, ip, reply ->
            {

                if (reply.succeeded())
                {
                    if (reply.result().body() != null)
                    {
                        promise.complete(reply.result().body());

                        records.put(ip, reply.result().body());
                    }

                    else
                    {
                        promise.fail("No record found");
                    }
                }

                else
                {
                    promise.fail(reply.cause().getMessage());
                }

            });
        }


        return promise.future();
    }

    /**
     * Adds a DNS record mapping the given IP address to the resolved hostname.
     * <p>
     * The mapping is added to the in-memory store (`records`) and also persisted to the
     * {@code TBL_DNS_RECORD} table in the configuration database.
     * <p>
     * This method is asynchronous, but it does not handle or log the result of the DB save operation.
     *
     * @param ip   The IP address being resolved.
     * @param host The hostname that the IP resolves to.
     *
     * @see Bootstrap#configDBService()
     */
    public void add(String ip, String host)
    {
        records.put(ip, host);

        Bootstrap.configDBService().save(DBConstants.TBL_DNS_RECORD, new JsonObject().put("ip", ip).put("host", host), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
        {
        });
    }

    public void clear()
    {
        records.clear();
    }

    /**
     * Adds the given IP address to the internal IP set.
     * <p>
     * This is typically used for tracking IPs that need to be resolved via reverse DNS lookup.
     *
     * @param ip The IP address to add.
     */
    public void add(String ip)
    {
        if (ips.size() < UNIQUE_IP_LIMIT)
        {
            ips.add(ip);
        }
    }

    /**
     * Returns a copy of the currently tracked IP addresses.
     * <p>
     * A defensive copy is returned to avoid concurrent modification issues, since Java does not have
     * a built-in thread-safe {@code Set} like {@code ConcurrentHashSet}.
     *
     * @return A {@code Set} containing the IP addresses.
     */
    public Set<String> getIps()
    {
        return new HashSet<>(ips); // because java does not have concurrentHashSet....
    }

    /**
     * Retrieves the hostname mapped to the given IP address, if available.
     *
     * @param ip The IP address for which to retrieve the hostname.
     * @return The resolved hostname, or {@code null} if no mapping is found.
     */
    public String get(String ip)
    {
        return records.get(ip);
    }
    public ConcurrentHashMap<String, String> copy()
    {
        return new ConcurrentHashMap<>(records);
    }
}
