package com.mindarray.core;

import com.mindarray.Bootstrap;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;

public class Core {

    private static Core instance;

    private Core() {}

    public static Core getInstance() {

        if (instance == null) {
            instance = new Core();
        }

        return instance;

    }

    public static <T> Future<T> request(String address, JsonObject message) {

        var future = Promise.<T>promise();

        Bootstrap.vertx().eventBus().<T>request(address, message).onComplete(reply -> {
            if (reply.succeeded()) {
                future.complete(reply.result().body());
            } else {
                future.fail(reply.cause());
            }
        });

        return future.future();
    }

    public static <T> Future<T> request(String address, JsonObject message, DeliveryOptions deliveryOptions) {

        var future = Promise.<T>promise();

        Bootstrap.vertx().eventBus().<T>request(address, message, deliveryOptions).onComplete(reply -> {
            if (reply.succeeded()) {
                future.complete(reply.result().body());
            } else {
                future.fail(reply.cause());
            }
        });

        return future.future();
    }

}
