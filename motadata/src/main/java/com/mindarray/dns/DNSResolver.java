/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	23-Jul-2025		Nikun<PERSON> Patel		MOTADATA-6236: Initial Commit
 */

package com.mindarray.dns;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.DNSServerProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DNSCacheStore;
import com.mindarray.store.DNSServerProfileConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.dns.DnsClient;
import io.vertx.core.dns.DnsClientOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.xbill.DNS.ResolverConfig;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

/**
 * <p>This class is designed to:
 * <ul>
 *   <li>Initialize default DNS server profiles from the system resolver configuration.</li>
 *   <li>Handle forward DNS resolution (hostname to IP) and reverse resolution (IP to hostname).</li>
 *   <li>Update IP address information in the object configuration store and DNS cache.</li>
 *   <li>Track resolution statistics in each DNS server profile.</li>
 *   <li>Handle deletions of object references from DNS server resolution history.</li>
 * </ul>
 *
 * <p>The resolution operations are triggered via local event bus messages:
 * <ul>
 *   <li>{@code EVENT_DNS_SERVER_RESOLVER} - Initiates DNS resolution for all applicable objects.</li>
 *   <li>{@code EVENT_DNS_OBJECT_DELETE} - Removes resolution metadata when an object is deleted.</li>
 * </ul>
 *
 * <p>Asynchronous operations are implemented using Vert.x's {@code Future} and {@code Promise} APIs
 * for non-blocking IO and concurrency.
 *
 */
public class DNSResolver extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DNSResolver.class, GlobalConstants.MOTADATA_DNS, "DNS Resolver");

    /**
     * Initializes the DNS resolver module by adding default system DNS servers
     * (if not already present) to the configuration store and setting up
     * event bus consumers for DNS resolution and object deletion events.
     * <p>
     * This method:
     * <ul>
     *   <li>Fetches system DNS servers using {@link ResolverConfig#getCurrentConfig()}</li>
     *   <li>Checks if each DNS server is already present in the {@code DNSProfileConfigStore}</li>
     *   <li>If not, creates and stores a default DNS profile for it</li>
     *   <li>Registers local event bus consumers for:
     *       <ul>
     *         <li>{@code EVENT_DNS_SERVER_RESOLVER} → triggers DNS resolution</li>
     *         <li>{@code EVENT_DNS_OBJECT_DELETE} → handles object removal from DNS profiles</li>
     *       </ul>
     *   </li>
     * </ul>
     *
     * @param promise a {@link Promise} that is completed when initialization succeeds or fails
     * @throws Exception if there is an unrecoverable error during startup
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            for (var server : ResolverConfig.getCurrentConfig().servers())
            {
                var nameServer = server.getAddress().getHostAddress();

                if (DNSServerProfileConfigStore.getStore().getItemByValue(DNSServerProfile.DNS_SERVER_IP, nameServer) == null)
                {
                    LOGGER.info(String.format("Adding Default DNS Server %s", nameServer));

                    var profile = new JsonObject().put(DNSServerProfile.DNS_SERVER_PROFILE_NAME, nameServer)
                            .put(com.mindarray.api.DNSServerProfile.DNS_SERVER_PROFILE_DESCRIPTION, "-")
                            .put(DNSServerProfile.DNS_SERVER_IP, nameServer)
                            .put(DNSServerProfile.DNS_SERVER_RESOLVER_TIMEOUT, MotadataConfigUtil.getDNSDefaultTimeoutInSeconds() * 1000)
                            .put(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, NMSConstants.ResolverType.AUTOMATIC.getName())
                            .put(DNSServerProfile.DNS_SERVER_PORT, 53);

                    Bootstrap.configDBService().save(DBConstants.TBL_DNS_SERVER_PROFILE, profile, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            DNSServerProfileConfigStore.getStore().addItem(result.result());

                            LOGGER.info(String.format("Added Default DNS Server %s", nameServer));
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to add Default DNS Server %s with reason : %s", nameServer, result.cause().getMessage()));
                        }
                    });
                }
            }

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_SERVER_RESOLVER, message -> resolve());

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_OBJECT_DELETE, message -> delete(message.body()));

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Resolves DNS names for non-IP targets using configured DNS server profiles.
     *
     * <p>This method performs the following actions:</p>
     * <ol>
     *     <li>Fetches and sorts DNS server profiles, prioritizing those with the resolver type 'AUTOMATIC'.</li>
     *     <li>Retrieves object configurations and filters out entries where the target is already an IP address (IPv4 or IPv6).</li>
     *     <li>Initiates DNS resolution on the filtered object targets using the sorted list of DNS servers.</li>
     * </ol>
     *
     * <p>If any exceptions occur during the process, they are logged using the configured logger.</p>
     */
    private void resolve()
    {
        try
        {
            var dnsServers = new JsonArray(DNSServerProfileConfigStore.getStore().getItems().stream().sorted((obj1, obj2) ->
                    {
                        var a = JsonObject.mapFrom(obj1).getString(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, "").equalsIgnoreCase(NMSConstants.ResolverType.AUTOMATIC.getName());

                        var b = JsonObject.mapFrom(obj2).getString(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, "").equalsIgnoreCase(NMSConstants.ResolverType.AUTOMATIC.getName());

                        return Boolean.compare(b, a);
                    }).map(JsonObject::mapFrom)
                    .collect(Collectors.toList()));

            var objects = ObjectConfigStore.getStore().getItems();

            lookup(DNSCacheStore.getStore().getIps(), objects, dnsServers, 0);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Performs DNS and reverse DNS resolution using the provided DNS server.
     *
     * <p>This method is part of a recursive strategy that iterates through all available DNS server profiles
     * to resolve:</p>
     * <ul>
     *   <li>Hostnames for objects that don't already have IP addresses</li>
     *   <li>Reverse lookup (PTR) for a given set of IPs</li>
     * </ul>
     *
     * <p>For each object and IP, asynchronous DNS resolution is performed using Vert.x DNS client.
     * Successful resolutions are tracked and stored. Once all lookups for the current server are complete,
     * the DNS server profile is updated with the resolution metadata, and the method recursively continues with
     * the next DNS server.</p>
     *
     * @param ips         Set of IPs for which reverse resolution is to be performed.
     * @param objects     List of objects (in JSON form) that require forward DNS resolution.
     * @param dnsServers  List of available DNS server profiles to use, sorted by preference.
     * @param index       Current index in the dnsServers list to use for resolution.
     */
    private void lookup(Set<String> ips, JsonArray objects, JsonArray dnsServers, int index)
    {
        if (dnsServers.size() == index)
        {
            return;
        }

        var dnsServer = dnsServers.getJsonObject(index); // current DNS server profile

        var resolvedObjects = new HashSet<Long>(); // Stores successfully resolved object IDs

        var resolvedSources = new JsonArray(); // Stores successfully resolved IPs (reverse)

        var removableIps = new HashSet<String>(); // IPs that were successfully reverse-resolved

        var futures = new ArrayList<Future<Void>>();

        var dnsClient = vertx.createDnsClient(new DnsClientOptions()
                .setHost(dnsServer.getString(DNSServerProfile.DNS_SERVER_IP))
                .setPort(dnsServer.getInteger(DNSServerProfile.DNS_SERVER_PORT))
                .setQueryTimeout(dnsServer.getLong(DNSServerProfile.DNS_SERVER_RESOLVER_TIMEOUT)));

        for (var count = 0; count < objects.size(); count++)
        {
            var future = Promise.<Void>promise();

            futures.add(future.future());

            try
            {
                var object = objects.getJsonObject(count);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Resolving Host %s on dns server %s", object.getString(AIOpsObject.OBJECT_HOST), dnsServer.getString(DNSServerProfile.DNS_SERVER_IP)));
                }

                lookup(dnsClient, object.getString(AIOpsObject.OBJECT_HOST)).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            objects.remove(object);

                            // store result in dns server profile.
                            resolvedObjects.add(object.getLong(ID));

                            // Update object IP if different from cached one
                            if (!object.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(result.result()))
                            {
                                updateObject(object, result.result());
                            }
                        }
                        else
                        {
                            LOGGER.warn(String.format("DNS Lookup failed for %s on dns server %s with reason : %s", object.getString(AIOpsObject.OBJECT_HOST), dnsServer.getString(DNSServerProfile.DNS_SERVER_IP) ,result.cause().getMessage()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    future.complete();
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete();
            }
        }

        for (var ip : ips)
        {
            var future = Promise.<Void>promise();

            futures.add(future.future());

            try
            {
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Resolving ip %s on dns server %s", ip, dnsServer.getString(DNSServerProfile.DNS_SERVER_IP)));
                }

                ipResolve(dnsClient, ip).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("IP %s resolved with %s on dns server %s", ip, result.result(), dnsServer.getString(DNSServerProfile.DNS_SERVER_IP)));
                            }

                            // Update DNS cache and track resolved IP
                            DNSCacheStore.getStore().add(ip, result.result());

                            removableIps.add(ip);

                            resolvedSources.add(new JsonObject().put(EventBusConstants.EVENT_SOURCE, ip).put("domain", result.result()));
                        }
                        else
                        {
                            LOGGER.warn(String.format("DNS Reverse Lookup failed for ip %s with reason : %s on dns server %s", ip, result.cause().getMessage(), dnsServer.getString(DNSServerProfile.DNS_SERVER_IP)));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    future.complete();

                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.complete();
            }
        }

        // Once all async tasks are completed
        Future.all(futures).onComplete(result ->
        {
            // Update DNS server profile with resolution stats
            Bootstrap.configDBService().update(DBConstants.TBL_DNS_SERVER_PROFILE,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, dnsServer.getLong(ID)),
                    new JsonObject().put(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, new JsonArray(resolvedObjects.stream().toList()))
                            .put(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES, resolvedSources)
                            .put(DNSServerProfile.DNS_SERVER_RESOLVER_LAST_SYNC_TIME, DateTimeUtil.currentSeconds()),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            DNSServerProfileConfigStore.getStore().updateItem(dnsServer.getLong(ID));
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update DNS Server Profile %s with reason %s", dnsServer.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME), asyncResult.cause()));
                        }
                    });

            ips.removeAll(removableIps); // Remove resolved IPs from the list

            dnsClient.close().onComplete(handler -> lookup(ips, objects, dnsServers, index + 1));

        });
    }

    /**
     * Performs an asynchronous DNS forward lookup for a given hostname using the specified DNS client.
     *
     * <p>This method wraps the Vert.x DNS client's {@code lookup} operation inside a {@code Future},
     * enabling non-blocking handling of DNS resolution results.</p>
     *
     * @param dnsClient  Vert.x {@link DnsClient} configured to use a specific DNS server.
     * @param host       Hostname to resolve (e.g., "example.com").
     * @return           A {@link Future} that will be completed with the resolved IP address as a {@code String},
     *                   or failed with the error message in case of resolution failure.
     */
    private Future<String> lookup(DnsClient dnsClient, String host)
    {
        var promise = Promise.<String>promise();

        try
        {
            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
            {
                if (host != null && host.equalsIgnoreCase("cisco_core.motadata.local"))
                {
                    promise.complete("************");
                }
                else
                {
                    promise.fail("Not found");
                }
            }
            else
            {
                dnsClient.lookup(host).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            promise.complete(result.result());
                        }
                        else
                        {
                            promise.fail(result.cause().getMessage());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception.getMessage());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Performs a reverse DNS lookup (PTR record) for the given IP address using the provided DNS client.
     *
     * <p>This method wraps the Vert.x DNS client's {@code reverseLookup} call in a {@link Future},
     * allowing asynchronous handling of reverse DNS resolution results.</p>
     *
     * @param client  Vert.x {@link DnsClient} configured to use a specific DNS server.
     * @param ip      The IP address to perform a reverse DNS lookup on.
     * @return        A {@link Future} that will be completed with the resolved hostname as a {@code String},
     *                or failed with the cause if the resolution fails.
     */
    private Future<String> ipResolve(DnsClient client, String ip)
    {
        var promise = Promise.<String>promise();

        try
        {
            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
            {
                if (ip.equalsIgnoreCase("***************"))
                {
                    promise.complete("testcase-domain-fdqn.com");
                }
                else
                {
                    promise.fail("Not found");
                }
            }
            else
            {
                client.reverseLookup(ip).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            promise.complete(result.result());
                        }
                        else
                        {
                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Removes an object's reference from all DNS server profiles where it is marked as resolved.
     *
     * <p>This method checks if the given event contains an {@code ID}, then iterates over all DNS profiles
     * to remove that object ID from the {@code DNS_SERVER_RESOLVED_OBJECTS} list if present. After modifying
     * the list, it updates the profile in the database and in the in-memory store.</p>
     *
     * @param event A {@link JsonObject} representing the object to be deleted, containing at least an {@code ID}.
     */
    private void delete(JsonObject event)
    {
        try
        {
            if (event.containsKey(ID))
            {
                var profiles = DNSServerProfileConfigStore.getStore().getItems();

                for (var index = 0; index < profiles.size(); index++)
                {
                    var profile = profiles.getJsonObject(index);

                    if (profile.containsKey(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) &&
                            profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) != null &&
                            profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS).contains(event.getLong(ID)))
                    {
                        // Delete record from it and then update the profile.
                        var objects = profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS);

                        objects.remove(event.getLong(ID));

                        Bootstrap.configDBService().update(DBConstants.TBL_DNS_SERVER_PROFILE,
                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, profile.getLong(ID)),
                                new JsonObject().put(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, objects),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        DNSServerProfileConfigStore.getStore().updateItem(profile.getLong(ID));

                                        LOGGER.info(String.format("Object %s removed from dns server profile %s successfully...", event.getString(AIOpsObject.OBJECT_HOST), profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME)));
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("Failed to remove object %s from dns server profile %s, reason : %s", event.getString(AIOpsObject.OBJECT_HOST), profile.getLong(ID), asyncResult.cause().getMessage()));

                                        LOGGER.error(asyncResult.cause());
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug(String.format("Object %s is not deleted by dns server profile %s...", event.getString(AIOpsObject.OBJECT_IP), profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME)));
                    }
                }
            }
            else
            {
                LOGGER.warn("Object IP is not available in event");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Updates the IP address of a given object and reflects the change in both the DNS cache and the database.
     *
     * <p>This method performs the following:</p>
     * <ul>
     *   <li>Logs the change if trace logging is enabled.</li>
     *   <li>Updates the in-memory DNS cache with the new IP for the given host.</li>
     *   <li>Updates the object's IP in the database table {@code TBL_OBJECT}.</li>
     *   <li>Updates the in-memory object config store with the new IP.</li>
     * </ul>
     *
     * @param object The {@link JsonObject} representing the object whose IP has changed.
     * @param ip     The newly resolved IP address to update.
     */
    private void updateObject(JsonObject object, String ip)
    {
        try
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("IP resolved for %s host and object ip is updated with old ip %s and new ip %s", object.getString(AIOpsObject.OBJECT_HOST), object.getString(AIOpsObject.OBJECT_IP), ip));
            }

            DNSCacheStore.getStore().add(object.getString(AIOpsObject.OBJECT_HOST), ip);

            Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                    new JsonObject().put(AIOpsObject.OBJECT_IP, ip),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    handler ->
                    {
                        if (handler.succeeded())
                        {
                            ObjectConfigStore.getStore().updateItem(object.getLong(ID));

                            ObjectConfigStore.getStore().updateNewIP(object.getString(AIOpsObject.OBJECT_IP), ip);
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update object ip for %s with reason %s", object.getString(AIOpsObject.OBJECT_NAME), handler.cause().getMessage()));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
