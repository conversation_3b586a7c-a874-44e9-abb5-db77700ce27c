/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *   Date			 Author			           Notes
 *   28-Feb-2025     Smit Prajapati            MOTADATA-4956: Audit for Rule Based Tagging
 *   2-Apr-2025      Bharat                    MOTADATA-5637: Domain Mapping in Flow
 *   28-Feb-2025     Pruthviraj                MOTADATA-4904 : Audit for netroute and netroute policy added
 *   18-Apr-2025     Bharat                    MOTADATA-5954: Domain Mapping Module Added
 *   18-Apr-2025     Sankalp                   MOTADATA-5859 : System user will be dumped for audits from 127.0.0.1
 *   22-Apr-2025     Bharat                    MOTADATA-5822: Metric Explorer Enhancements
 *   24-Jun-2025     Yash Tiwari               MOTADATA-6528 : added explorer config store entry for common explorers
 *   23-Jul-2025	 Nikunj Patel              MOTADATA-6236: Audit for DNS-server added.
 * */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.visualization.VisualizationConstants;
import com.opencsv.CSVWriterBuilder;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.handler.sockjs.impl.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.FileWriter;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_GROUPS;
import static com.mindarray.api.AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Agent.AGENT_BUSINESS_HOUR_PROFILE;
import static com.mindarray.api.CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME;
import static com.mindarray.api.SNMPTrapProfile.SNMP_TRAP_PROFILE_NAME;
import static com.mindarray.api.User.USER_PASSWORD_LAST_UPDATED_TIME;
import static com.mindarray.api.User.USER_TEMPORARY_PASSWORD_CREATED_TIME;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.SNMP_OID_GROUP_OIDS;

public class AuditUtil extends AbstractVerticle
{
    public static final String AUDIT_MODULE = "audit.module";
    public static final String AUDIT_OPERATION = "audit.operation";
    public static final String AUDIT_USER = "audit.user";
    public static final String AUDIT_MESSAGE = "audit.message";
    public static final String AUDIT_STATUS = "audit.status";
    public static final String AUDIT_REMOTE_IP = "audit.remote.ip";
    private static final Logger LOGGER = new Logger(AuditUtil.class, GlobalConstants.MOTADATA_AUDIT, "Audit Util");
    private final Map<String, String> MODULES_BY_TABLE = Map.<String, String>ofEntries(Map.entry("tbl_config_agent", "agents"),
            Map.entry("tbl_config_application_mapper", "application-mappers"),
            Map.entry("tbl_config_business_hour", "business-hours"),
            Map.entry("tbl_config_credential_profile", "credential-profiles"),
            Map.entry("tbl_config_custom_monitoring_field", "custom-monitoring-fields"),
            Map.entry("tbl_config_discovery", "discoveries"),
            Map.entry("tbl_config_dependency_mapper", "dependency-mappers"),
            Map.entry("tbl_config_dns_server", "dns-server-configuration"),
            Map.entry("tbl_config_group", "groups"),
            Map.entry("tbl_config_ldap_server", "ldap-servers"),
            Map.entry("tbl_config_mail_server", "mail-server-configuration"),
            Map.entry("tbl_config_rebranding", "rebranding"),
            Map.entry("tbl_config_metric_plugin", "metric-plugins"),
            Map.entry("tbl_config_object", "objects"),
            Map.entry("tbl_config_password_policy", "password-policy"),
            Map.entry("tbl_config_protocol_mapper", "protocol-mappers"),
            Map.entry("tbl_config_remote_event_processor", "remote-event-processors"),
            Map.entry("tbl_config_scheduler", "schedulers"),
            Map.entry("tbl_config_sms_gateway", "sms-gateway-configuration"),
            Map.entry("tbl_config_snmp_device_catalog", "snmp-device-catalogs"),
            Map.entry("tbl_config_oid_group", "snmp-oid-groups"),
            Map.entry("tbl_config_snmp_trap_forwarder", "snmp-trap-forwarders"),
            Map.entry("tbl_config_snmp_trap_listener", "snmp-trap-listener-configuration"),
            Map.entry("tbl_config_snmp_trap_profile", "snmp-trap-profiles"),
            Map.entry("tbl_config_system_file", "system-files"),
            Map.entry("tbl_config_system_process", "system-processes"),
            Map.entry("tbl_config_system_service", "system-services"),
            Map.entry("tbl_config_user_role", "user-roles"),
            Map.entry("tbl_config_metric", "metrics"),
            Map.entry("tbl_config_explorer", "explorers"),
            Map.entry("tbl_config_widget", "widgets"),
            Map.entry("tbl_config_log_parser", "log-parsers"),
            Map.entry("tbl_config_log_parser_plugin", "log-parser-plugins"),
            Map.entry("tbl_config_flow_sampling_rate", "flow-sampling-rates"),
            Map.entry("tbl_config_flow_ip_group", "flow-ip-groups"),
            Map.entry("tbl_config_dashboard", "dashboards"),
            Map.entry("tbl_config_metric_policy", "metric-policies"),
            Map.entry("tbl_config_event_policy", "event-policies"),
            Map.entry("tbl_config_user", "users"),
            Map.entry("tbl_config_template", "templates"),
            Map.entry("tbl_config_flow_setting", "flow-settings"),
            Map.entry("tbl_config_event_source", "event-sources"),
            Map.entry("tbl_config_runbook_plugin", "runbook-plugins"),
            Map.entry("tbl_config_topology_plugin", "topology-plugins"),
            Map.entry("tbl_config_report", "reports"),
            Map.entry("tbl_config_data_retention_policy", "data-retention-policy"),
            Map.entry("tbl_config_configuration", "configurations"),
            Map.entry("tbl_config_config_template", "config-templates"),
            Map.entry("tbl_config_backup_profile", "backup-profiles"),
            Map.entry("tbl_config_storage_profile", "storage-profiles"),
            Map.entry("tbl_config_integration", "integrations"),
            Map.entry("tbl_config_flow_as_mapper", "flow-as-mappers"),
            Map.entry("tbl_config_flow_domain_mapper", "flow-domain-mappers"),
            Map.entry("tbl_config_flow_geolocation_mapper", "flow-geolocation-mappers"),
            Map.entry("tbl_config_flow_ip_mapper", "flow-ip-mappers"),
            Map.entry("tbl_config_personal_access_token", "personal-access-tokens"),
            Map.entry("tbl_config_log_forwarder", "log-forwarders"),
            Map.entry("tbl_config_log_collector", "log-collectors"),
            Map.entry("tbl_config_compliance_rule", "compliance-rules"),
            Map.entry("tbl_config_compliance_policy", "compliance-policies"),
            Map.entry("tbl_config_compliance_benchmark", "compliance-benchmarks"),
            Map.entry("tbl_config_two_factor_authentication", "two-factor-authentication"),
            Map.entry("tbl_config_tag_rule", "tag-rules"),
            Map.entry("tbl_config_netroute", "netroutes"),
            Map.entry("tbl_config_netroute_policy", "netroute-policies"),
            Map.entry("tbl_config_dns_server_profile", "dns-server-profiles")


    );

    private final Map<String, String> ENTITIES_BY_TABLE = Map.<String, String>ofEntries(Map.entry("tbl_config_agent", "Agent"),
            Map.entry("tbl_config_application_mapper", "Application Mapper"),
            Map.entry("tbl_config_business_hour", "Monitoring Hour"),
            Map.entry("tbl_config_credential_profile", "Credential Profile"),
            Map.entry("tbl_config_custom_monitoring_field", "Custom Monitoring Field"),
            Map.entry("tbl_config_discovery", "Discovery"),
            Map.entry("tbl_config_dependency_mapper", "Dependency Mapper"),
            Map.entry("tbl_config_dns_server", "DNS Server Configuration"),
            Map.entry("tbl_config_group", "Group"),
            Map.entry("tbl_config_ldap_server", "LDAP Server"),
            Map.entry("tbl_config_mail_server", "Mail Server Configuration"),
            Map.entry("tbl_config_rebranding", "Rebranding"),
            Map.entry("tbl_config_metric_plugin", "Metric Plugin"),
            Map.entry("tbl_config_object", "Monitor"),
            Map.entry("tbl_config_password_policy", "Password Policy"),
            Map.entry("tbl_config_protocol_mapper", "Protocol Mapper"),
            Map.entry("tbl_config_remote_event_processor", "Remote Event Processor"),
            Map.entry("tbl_config_scheduler", "Scheduler"),
            Map.entry("tbl_config_sms_gateway", "SMS Gateway Configuration"),
            Map.entry("tbl_config_snmp_device_catalog", "SNMP Device Catalog"),
            Map.entry("tbl_config_oid_group", "SNMP OID Group"),
            Map.entry("tbl_config_snmp_trap_forwarder", "SNMP Trap Forwarder"),
            Map.entry("tbl_config_snmp_trap_listener", "SNMP Trap Listener"),
            Map.entry("tbl_config_snmp_trap_profile", "SNMP Trap Profile"),
            Map.entry("tbl_config_system_file", "System File"),
            Map.entry("tbl_config_system_process", "System Process"),
            Map.entry("tbl_config_system_service", "System Service"),
            Map.entry("tbl_config_user_role", "User Role"),
            Map.entry("tbl_config_metric", "Metric"),
            Map.entry("tbl_config_metric_explorer", "Metric Explorer"),
            Map.entry("tbl_config_widget", "Widget"),
            Map.entry("tbl_config_log_parser", "Log Parser"),
            Map.entry("tbl_config_log_parser_plugin", "Log Parser Plugin"),
            Map.entry("tbl_config_flow_sampling_rate", "Flow Sampling Rate"),
            Map.entry("tbl_config_flow_ip_group", "Flow IP Group"),
            Map.entry("tbl_config_dashboard", "Dashboard"),
            Map.entry("tbl_config_metric_policy", "Metric Policy"),
            Map.entry("tbl_config_event_policy", "Event Policy"),
            Map.entry("tbl_config_user", "User"),
            Map.entry("tbl_config_template", "Template"),
            Map.entry("tbl_config_flow_setting", "Flow Settings"),
            Map.entry("tbl_config_event_source", "Event Source"),
            Map.entry("tbl_config_runbook_plugin", "Runbook Plugin"),
            Map.entry("tbl_config_topology_plugin", "Topology Plugin"),
            Map.entry("tbl_config_report", "Report"),
            Map.entry("tbl_config_data_retention_policy", "Data Retention Policy"),
            Map.entry("tbl_config_configuration", "Configuration"),
            Map.entry("tbl_config_config_template", "Config Template"),
            Map.entry("tbl_config_backup_profile", "Backup Profile"),
            Map.entry("tbl_config_storage_profile", "Storage Profile"),
            Map.entry("tbl_config_integration", "Integration"),
            Map.entry("tbl_config_flow_as_mapper", "Flow AS Mapper"),
            Map.entry("tbl_config_flow_domain_mapper", "Flow Domain Mapper"),
            Map.entry("tbl_config_flow_geolocation_mapper", "Flow Geolocation Mapper"),
            Map.entry("tbl_config_flow_ip_mapper", "Flow IP Mapper"),
            Map.entry("tbl_config_personal_access_token", "Personal Access Token"),
            Map.entry("tbl_config_log_collector", "Log Collector"),
            Map.entry("tbl_config_log_forwarder", "Log Forwarder"),
            Map.entry("tbl_config_compliance_rule", "Compliance Rule"),
            Map.entry("tbl_config_compliance_benchmark", "Compliance Benchmark"),
            Map.entry("tbl_config_compliance_policy", "Compliance Policy"),
            Map.entry("tbl_config_two_factor_authentication", "Two Factor Authentication"),
            Map.entry("tbl_config_tag_rule", "Tag Rule"),
            Map.entry("tbl_config_netroute", "NetRoute"),
            Map.entry("tbl_config_netroute_policy", "NetRoute Policy"),
            Map.entry("tbl_config_dns_server_profile", "DNS Server Profile")
    );

    //if we do not want any particular entity fields data to be dumped into clickhouse for audit purpose
    //entity means from its json file entity.name value
    //multiple fields of same entity can be ignored by adding its fields
    private final Map<String, JsonArray> PASSOVER_FIELDS_BY_ENTITY = Map.ofEntries(Map.entry(Entity.AGENT.getName(), new JsonArray().add(NMSConstants.PROCESSES).add(NMSConstants.SERVICES)), Map.entry("User", new JsonArray().add("ui.columns")), Map.entry(Entity.SCHEDULER.getName(), new JsonArray().add(RESULT)), Map.entry(Entity.METRIC.getName(), new JsonArray().add(Metric.METRIC_CONTEXT).add(NMSConstants.OBJECTS)));
    //if we want to replace some audit logs of particular module to be displayed as another module
    //entity means from its json file entity.name value
    //at max one field can be provided which is unique for its identification
    private final Map<String, JsonObject> SUBSTITUTE_MODULES_BY_ENTITY = Map.ofEntries(Map.entry(Entity.OBJECT.getName(), new JsonObject().put(AIOpsObject.OBJECT_AGENT, "agents")));
    //if we want to generate any custom level audit for any entity for its particular field
    //entity means from its json file entity.name value
    //at max one field can be provided as of now with its value as field name form which custom log is to be generated
    private final Map<String, String> CUSTOM_AUDIT_MODULES_BY_ENTITY = Map.ofEntries(Map.entry(Entity.AGENT.getName(), Agent.AGENT_CONFIGS), Map.entry(Entity.OBJECT.getName(), AIOpsObject.OBJECT_CUSTOM_FIELDS));
    //if we do not want any particular entity for comparing inner fields
    //entity means from its json file entity.name value
    //for usage purpose only key of map is considered as now if entity name provided will not go for inner comparison of json object keys directly will compare objects not inner context
    private final Map<String, String> PASSOVER_ENTITY_CONTEXT_FIELDS = Map.ofEntries(Map.entry("SNMP OID Group", SNMP_OID_GROUP_OIDS));
    //in order to receive any user notification for crud related operations kindly just need to add its entity name with blank json array if require to ignore any particular request you can add its request name it's user notification will not be generated
    private final Map<String, JsonArray> USER_NOTIFICATION_PASSOVER_REQUESTS_BY_ENTITY = Map.ofEntries(Map.entry(Entity.USER.getName(), new JsonArray().add(REQUEST_LOGIN)), Map.entry(Entity.OBJECT.getName(), new JsonArray()));

    private final StringBuilder builder = new StringBuilder(0);
    private Set<String> mappers;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        vertx.eventBus().<JsonObject>localConsumer(EVENT_AUDIT, message ->
        {
            var request = message.body().getString(APIConstants.REQUEST);

            var status = message.body().getBoolean(STATUS, Boolean.FALSE);

            var value = EMPTY_VALUE;

            var schema = CommonUtil.getEntitySchema(MODULES_BY_TABLE.get(message.body().getString(ENTITY_TABLE)));

            var fieldName = EMPTY_VALUE;

            if (schema != null)
            {
                try
                {
                    var entityName = schema.getString(ENTITY_NAME);

                    var substituteModules = SUBSTITUTE_MODULES_BY_ENTITY.get(entityName);

                    if (substituteModules != null)
                    {
                        for (var key : substituteModules.getMap().keySet())
                        {
                            if (message.body().getJsonObject(request.equalsIgnoreCase(REQUEST_UPDATE) ? CONFIG_OLD_PROPS : CONFIG_UPDATED_PROPS) != null && message.body().getJsonObject(request.equalsIgnoreCase(REQUEST_UPDATE) ? CONFIG_OLD_PROPS : CONFIG_UPDATED_PROPS).containsKey(key))
                            {
                                schema = CommonUtil.getEntitySchema(substituteModules.getString(key));
                            }
                        }
                    }

                    switch (request)
                    {
                        case REQUEST_CREATE ->
                        {
                            if (message.body().getJsonObject(CONFIG_UPDATED_PROPS) != null && message.body().getJsonObject(CONFIG_UPDATED_PROPS).getLong(ID) > 0)
                            {
                                fieldName = getEntityName(message.body().getJsonObject(CONFIG_UPDATED_PROPS).getLong(ID), message.body().getString(ENTITY_TABLE), message.body().getJsonObject(CONFIG_UPDATED_PROPS));
                            }

                            if (fieldName != null)
                            {
                                value = (status) ? String.format(InfoMessageConstants.AUDIT_ENTITY_CREATED, fieldName, entityName)
                                        : message.body().getString(ERROR);
                            }
                        }

                        case REQUEST_DELETE ->
                        {
                            if (message.body().getJsonObject(CONFIG_UPDATED_PROPS) != null && message.body().getJsonObject(CONFIG_UPDATED_PROPS).getLong(ID) > 0)
                            {
                                fieldName = getEntityName(message.body().getJsonObject(CONFIG_UPDATED_PROPS).getLong(ID), message.body().getString(ENTITY_TABLE), message.body().getJsonObject(CONFIG_UPDATED_PROPS));
                            }

                            if (fieldName != null)
                            {

                                value = (status) ? String.format(InfoMessageConstants.AUDIT_ENTITY_DELETED, fieldName, entityName)
                                        : String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, entityName, message.body().getString(ERROR));
                            }
                        }

                        case REQUEST_UPDATE ->
                        {
                            if (status)
                            {
                                if (message.body().getJsonObject(CONFIG_OLD_PROPS) != null && message.body().getJsonObject(CONFIG_UPDATED_PROPS) != null)
                                {
                                    var oldProps = message.body().getJsonObject(CONFIG_OLD_PROPS);

                                    if (oldProps.getLong(ID) > 0)
                                    {
                                        fieldName = getEntityName(oldProps.getLong(ID).longValue(), message.body().getString(ENTITY_TABLE));
                                    }

                                    if (fieldName != null)
                                    {
                                        var newProps = message.body().getJsonObject(CONFIG_UPDATED_PROPS);

                                        var previousContext = new JsonObject();

                                        var updatedContext = new JsonObject();

                                        var fields = mapFieldTitles(schema);

                                        newProps.getMap().keySet().stream().filter(key1 -> newProps.getValue(key1) != null && (!oldProps.containsKey(key1)
                                                || !oldProps.getValue(key1).equals(newProps.getValue(key1))) && (PASSOVER_FIELDS_BY_ENTITY.get(entityName) == null || !PASSOVER_FIELDS_BY_ENTITY.get(entityName).contains(key1))).forEach(key ->
                                        {

                                            if (CUSTOM_AUDIT_MODULES_BY_ENTITY.get(entityName) != null && CUSTOM_AUDIT_MODULES_BY_ENTITY.get(entityName).equalsIgnoreCase(key))
                                            {
                                                auditCustomFieldChanges(entityName, key, oldProps, newProps, previousContext, updatedContext);
                                            }
                                            //for inner context matching if any value update found
                                            else if (PASSOVER_ENTITY_CONTEXT_FIELDS.get(entityName) == null && newProps.getValue(key) instanceof JsonObject && oldProps.getValue(key) instanceof JsonObject)
                                            {
                                                //some of the scenario may contain new keys in oldProps like removing custom fields
                                                Stream.concat(newProps.getJsonObject(key).getMap().keySet().stream(), oldProps.getJsonObject(key).getMap().keySet().stream())
                                                        .collect(Collectors.toSet()).stream().filter(key2 -> (!oldProps.getJsonObject(key).containsKey(key2) || !newProps.getJsonObject(key).containsKey(key2)
                                                                || !oldProps.getJsonObject(key).getValue(key2).equals(newProps.getJsonObject(key).getValue(key2)) && (PASSOVER_FIELDS_BY_ENTITY.get(entityName) == null || !PASSOVER_FIELDS_BY_ENTITY.get(entityName).contains(key2)))).forEach(key3 ->
                                                        {
                                                            previousContext.put(key3, oldProps.getJsonObject(key).getValue(key3) != null ? getUpdatedEntityName(oldProps.getJsonObject(key).getValue(key3), key3) : EMPTY_VALUE);

                                                            updatedContext.put(key3, newProps.getJsonObject(key).getValue(key3) != null ? getUpdatedEntityName(newProps.getJsonObject(key).getValue(key3), key3) : EMPTY_VALUE);

                                                        });

                                            }
                                            //some of the scenario may add new keys in newProps like adding custom fields for the first time
                                            else if (newProps.getValue(key) instanceof JsonObject && oldProps.getValue(key) == null)
                                            {
                                                newProps.getJsonObject(key).getMap().keySet().forEach(key3 ->
                                                {
                                                    previousContext.put(key3, EMPTY_VALUE);

                                                    updatedContext.put(key3, getUpdatedEntityName(newProps.getJsonObject(key).getValue(key3, EMPTY_VALUE), key3));
                                                });
                                            }

                                            else
                                            {

                                                previousContext.put(fields.getString(key) != null ? fields.getString(key) : key, getUpdatedEntityName(oldProps.getValue(key, EMPTY_VALUE), key));

                                                updatedContext.put(fields.getString(key) != null ? fields.getString(key) : key, getUpdatedEntityName(newProps.getValue(key, EMPTY_VALUE), key));
                                            }
                                        });

                                        var previousContextWithMaskedSensitiveFields = CommonUtil.maskSensitiveFields(previousContext);

                                        var updatedContextWithMaskedSensitiveFields = CommonUtil.maskSensitiveFields(updatedContext);

                                        if (!previousContextWithMaskedSensitiveFields.isEmpty() && !updatedContextWithMaskedSensitiveFields.isEmpty())
                                        {
                                            value = String.format(InfoMessageConstants.AUDIT_ENTITY_UPDATED, fieldName, entityName) + GlobalConstants.NEW_LINE + new JsonObject().put("old.prop.value", previousContextWithMaskedSensitiveFields).put("new.prop.value", updatedContextWithMaskedSensitiveFields).encodePrettily();
                                        }
                                    }
                                }
                            }
                            else
                            {
                                value = String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, entityName, message.body().getString(ERROR));
                            }
                        }

                        default -> value = message.body().getString(MESSAGE);
                    }

                    if (CommonUtil.isNotNullOrEmpty(value))
                    {
                        if (USER_NOTIFICATION_PASSOVER_REQUESTS_BY_ENTITY.containsKey(entityName) && !USER_NOTIFICATION_PASSOVER_REQUESTS_BY_ENTITY.get(entityName).contains(request))
                        {
                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(MESSAGE, request.equalsIgnoreCase(REQUEST_UPDATE) ? String.format(InfoMessageConstants.AUDIT_ENTITY_UPDATED, fieldName, entityName) : value).put(UserNotificationUtil.USER_NOTIFICATION_TYPE, UserNotificationUtil.USER_NOTIFICATION_TYPE_SYSTEM).put(STATUS, status ? STATUS_SUCCEED : STATUS_FAIL).put(ENTITY_NAME, entityName));
                        }

                        DatastoreConstants.write(new JsonObject()
                                .put(PLUGIN_ID, DatastoreConstants.PluginId.AUDIT_EVENT.getName())
                                .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.AUDIT.ordinal())
                                .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                .put(AUDIT_MODULE, entityName).put(AUDIT_REMOTE_IP, message.body().getString(REMOTE_ADDRESS)).put(AUDIT_OPERATION, StringUtils.capitalize(message.body().getString(REQUEST)))
                                .put(AUDIT_USER, message.body().getString(REMOTE_ADDRESS).equalsIgnoreCase(GlobalConstants.SYSTEM_REMOTE_ADDRESS) ? SYSTEM_USER : message.body().getString(User.USER_NAME).toLowerCase())
                                .put(AUDIT_MESSAGE, value).put(AUDIT_STATUS, status ? STATUS_SUCCEED : STATUS_FAIL)
                                .put(EventBusConstants.EVENT_SOURCE, message.body().getString(REMOTE_ADDRESS)), VisualizationConstants.VisualizationDataSource.AUDIT.getName(), mappers, builder);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_AUDIT_EXPORT, message ->
        {
            var event = message.body();

            try (var writer = new CSVWriterBuilder(new FileWriter(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(UI_EVENT_UUID).trim() + ".csv")).withSeparator(',').withLineEnd("\n").build())
            {
                var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                var dateFormat = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);

                JsonObject result;

                if (event.containsKey("unpacked"))
                {
                    result = event.getJsonObject(RESULT);
                }
                else
                {
                    result = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(RESULT)), LOGGER, false, null, false, true);
                }
                //  header
                writer.writeNext(new String[]{"Operation Type", "User", "Message", "Remote IP", "Time", "Module", "Status"}, true);

                if (result.getJsonArray(RESULT) != null)
                {
                    var rows = result.getJsonArray(RESULT);

                    for (var index = 0; index < rows.size(); index++)
                    {
                        if (rows.getJsonObject(index) != null)
                        {
                            var row = rows.getJsonObject(index);

                            writer.writeNext(new String[]
                                    {
                                            row.getString("audit.operation^value", EMPTY_VALUE),
                                            row.getString("audit.user^value", EMPTY_VALUE),
                                            row.getString("audit.message^value", EMPTY_VALUE),
                                            row.getString("audit.remote.ip^value", EMPTY_VALUE),
                                            dateFormat.format(new Date(row.getLong("Timestamp", DateTimeUtil.currentSeconds()))),
                                            row.getString("audit.module^value", EMPTY_VALUE),
                                            row.getString("audit.status^value", EMPTY_VALUE),
                                    }, true);

                            writer.flushQuietly();
                        }
                    }

                    writer.flushQuietly();

                    EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, new JsonObject().put(GlobalConstants.FILE_NAME, event.getString(UI_EVENT_UUID).trim() + ".csv").put(STATUS, STATUS_SUCCEED).put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)));
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    private JsonObject mapFieldTitles(JsonObject schema)
    {
        var properties = schema.getJsonArray(APIConstants.ENTITY_PROPERTY);

        var fields = new JsonObject();

        if (properties != null && !properties.isEmpty())
        {
            for (var index = 0; index < properties.size(); index++)
            {
                var context = properties.getJsonObject(index);

                if (context != null && !context.isEmpty() && !context.getString(APIConstants.ENTITY_PROPERTY_TYPE).equalsIgnoreCase(APIConstants.FIELD_TYPE_SECURED))
                {
                    fields.put(context.getString(APIConstants.ENTITY_PROPERTY_NAME), context.getString(ENTITY_PROPERTY_TITLE));
                }
            }
        }

        return fields;
    }

    private String getUpdatedEntityName(Object keys, String value)
    {
        var result = EMPTY_VALUE;

        try
        {
            var entityNames = new StringBuilder();

            if (keys instanceof JsonArray)
            {
                ((JsonArray) keys).forEach(key ->
                {
                    try
                    {
                        entityNames.append(key instanceof JsonObject ? StringEscapeUtils.escapeJavaScript(JsonObject.mapFrom(key).encode()) : getEntityName(key, value)).append(COMMA_SEPARATOR);
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }

            else
            {
                entityNames.append(getEntityName(keys, value)).append(COMMA_SEPARATOR);
            }

            result = !entityNames.isEmpty() ? StringEscapeUtils.escapeJavaScript(entityNames.substring(0, entityNames.lastIndexOf(",")).trim()) : StringEscapeUtils.escapeJavaScript(CommonUtil.getString(entityNames));

            if (value.equalsIgnoreCase("policy.actions") || value.equalsIgnoreCase("WARNING") || value.equalsIgnoreCase("CRITICAL") || value.equalsIgnoreCase("MAJOR"))
            {

                for (var id : result.split(COMMA_SEPARATOR))
                {

                    result = result.replace(id, CommonUtil.getLong(id) != DUMMY_ID ? RunbookPluginConfigStore.getStore().getItem(CommonUtil.getLong(id)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) : id);

                }

            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private Object getEntityName(Object key, String value)
    {
        var result = key;

        try
        {
            if (StringUtils.isNumeric(CommonUtil.getString(key)) && CommonUtil.getLong(key) > 0)
            {
                result = getEntityName(CommonUtil.getLong(key), value, null);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private String getEntityName(long id, String entityName)
    {
        return getEntityName(id, entityName, null);
    }

    private String getEntityName(long id, String table, JsonObject dataContext)
    {
        var entityName = ENTITIES_BY_TABLE.getOrDefault(table, EMPTY_VALUE);

        return switch (table)
        {
            case TBL_APPLICATION_MAPPER ->
                    (dataContext != null) ? dataContext.getString(ApplicationMapper.APPLICATION_MAPPER_NAME) : ApplicationMapperConfigStore.getStore().getItem(id) != null ? ApplicationMapperConfigStore.getStore().getItem(id).getString(ApplicationMapper.APPLICATION_MAPPER_NAME) : entityName;

            case TBL_BUSINESS_HOUR, AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE, AGENT_BUSINESS_HOUR_PROFILE ->
                    (dataContext != null) ? dataContext.getString(BusinessHour.BUSINESS_HOUR_NAME) : BusinessHourConfigStore.getStore().getItem(id).getString(BusinessHour.BUSINESS_HOUR_NAME);

            case TBL_CREDENTIAL_PROFILE, CredentialProfile.CREDENTIAL_PROFILE_NAME,
                 Discovery.DISCOVERY_CREDENTIAL_PROFILES, Metric.METRIC_CREDENTIAL_PROFILE,
                 Configuration.CONFIG_CREDENTIAL_PROFILE ->
                    (dataContext != null) ? dataContext.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) : CredentialProfileConfigStore.getStore().getItem(id) != null ? CredentialProfileConfigStore.getStore().getItem(id).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) : entityName;

            case TBL_CUSTOM_MONITORING_FIELD ->
                    (dataContext != null) ? dataContext.getString(CUSTOM_MONITORING_FIELD_NAME) : CustomMonitoringFieldConfigStore.getStore().getItem(id) != null ? CustomMonitoringFieldConfigStore.getStore().getItem(id).getString(CUSTOM_MONITORING_FIELD_NAME) : entityName;

            case TBL_DISCOVERY ->
                    (dataContext != null) ? dataContext.getString(Discovery.DISCOVERY_NAME) : DiscoveryConfigStore.getStore().getItem(id) != null ? DiscoveryConfigStore.getStore().getItem(id).getString(Discovery.DISCOVERY_NAME) : entityName;

            case TBL_GROUP, User.USER_GROUPS, Group.FIELD_PARENT_GROUP, OBJECT_GROUPS,
                 Discovery.DISCOVERY_GROUPS ->
                    (dataContext != null) ? dataContext.getString(Group.FIELD_GROUP_NAME) : GroupConfigStore.getStore().getItem(id) != null ? GroupConfigStore.getStore().getItem(id).getString(Group.FIELD_GROUP_NAME) : entityName;

            case TBL_METRIC_PLUGIN ->
                    (dataContext != null) ? dataContext.getString(MetricPlugin.METRIC_PLUGIN_NAME) : MetricPluginConfigStore.getStore().getItem(id) != null ? MetricPluginConfigStore.getStore().getItem(id).getString(MetricPlugin.METRIC_PLUGIN_NAME) : entityName;

            case TBL_EXPLORER ->
                    (dataContext != null) ? dataContext.getString(Explorer.EXPLORER_NAME) : ExplorerConfigStore.getStore().getItem(id) != null ? ExplorerConfigStore.getStore().getItem(id).getString(Explorer.EXPLORER_NAME) : entityName;

            case TBL_OBJECT ->
                    (dataContext != null) ? dataContext.getString(AIOpsObject.OBJECT_NAME) : ObjectConfigStore.getStore().getItem(id) != null ? ObjectConfigStore.getStore().getItem(id).getString(AIOpsObject.OBJECT_NAME) : ArchivedObjectConfigStore.getStore().getItem(id).getString(AIOpsObject.OBJECT_NAME);

            case TBL_PROTOCOL_MAPPER ->
                    (dataContext != null) ? dataContext.getString(ProtocolMapper.PROTOCOL_MAPPER_NAME) : ProtocolMapperConfigStore.getStore().getItem(id) != null ? ProtocolMapperConfigStore.getStore().getItem(id).getString(ProtocolMapper.PROTOCOL_MAPPER_NAME) : entityName;

            case TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST,
                 AIOpsObject.OBJECT_EVENT_PROCESSORS ->
                    (dataContext != null) ? dataContext.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST) : RemoteEventProcessorConfigStore.getStore().getItem(id) != null ? RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST) : entityName;

            case TBL_SNMP_TRAP_FORWARDER ->
                    (dataContext != null) ? dataContext.getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_NAME) : SNMPTrapForwarderConfigStore.getStore().getItem(id) != null ? SNMPTrapForwarderConfigStore.getStore().getItem(id).getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_NAME) : entityName;

            case TBL_SNMP_TRAP_PROFILE, SNMPTrapForwarder.SNMP_TRAP_FORWARDER_PROFILES ->
                    (dataContext != null) ? dataContext.getString(SNMP_TRAP_PROFILE_NAME) : SNMPTrapProfileConfigStore.getStore().getItem(id) != null ? SNMPTrapProfileConfigStore.getStore().getItem(id).getString(SNMP_TRAP_PROFILE_NAME) : entityName;

            case TBL_USER_ROLE ->
                    (dataContext != null) ? dataContext.getString(UserRole.USER_ROLE_NAME) : UserRoleConfigStore.getStore().getItem(id) != null ? UserRoleConfigStore.getStore().getItem(id).getString(UserRole.USER_ROLE_NAME) : entityName;

            case TBL_USER ->
                    (dataContext != null) ? dataContext.getString(User.USER_NAME) : UserConfigStore.getStore().getItem(id) != null ? UserConfigStore.getStore().getItem(id).getString(User.USER_NAME) : entityName;

            case TBL_METRIC ->
                    (dataContext != null) ? (ObjectConfigStore.getStore().getItem(dataContext.getLong(Metric.METRIC_OBJECT)) != null ? ObjectConfigStore.getStore().getItem(dataContext.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_NAME) : ArchivedObjectConfigStore.getStore().getItem(dataContext.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_NAME)) + " Monitor " + dataContext.getString(Metric.METRIC_NAME) : ObjectConfigStore.getStore().getItem(MetricConfigStore.getStore().getItem(id).getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_NAME) + " Monitor " + MetricConfigStore.getStore().getItem(id).getString(Metric.METRIC_NAME);

            case TBL_SYSTEM_PROCESS ->
                    (dataContext != null) ? dataContext.getString(SystemProcess.SYSTEM_PROCESS) : SystemProcessConfigStore.getStore().getItem(id) != null ? SystemProcessConfigStore.getStore().getItem(id).getString(SystemProcess.SYSTEM_PROCESS) : entityName;

            case TBL_SYSTEM_SERVICE ->
                    (dataContext != null) ? dataContext.getString(SystemService.SYSTEM_SERVICE) : SystemServiceConfigStore.getStore().getItem(id) != null ? SystemServiceConfigStore.getStore().getItem(id).getString(SystemService.SYSTEM_SERVICE) : entityName;

            case TBL_SYSTEM_FILE ->
                    (dataContext != null) ? dataContext.getString(SystemFile.SYSTEM_FILE) : SystemFileConfigStore.getStore().getItem(id) != null ? SystemFileConfigStore.getStore().getItem(id).getString(SystemFile.SYSTEM_FILE) : entityName;

            case TBL_SNMP_DEVICE_CATALOG, OBJECT_SNMP_DEVICE_CATALOG ->
                    (dataContext != null) ? dataContext.getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_MODEL) : SNMPDeviceCatalogConfigStore.getStore().getItem(id) != null ? SNMPDeviceCatalogConfigStore.getStore().getItem(id).getString(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_MODEL) : entityName;

            case TBL_SNMP_OID_GROUP ->
                    (dataContext != null) ? dataContext.getString(SNMPOIDGroup.OID_GROUP_NAME) : SNMPOIDGroupConfigStore.getStore().getItem(id) != null ? SNMPOIDGroupConfigStore.getStore().getItem(id).getString(SNMPOIDGroup.OID_GROUP_NAME) : entityName;

            case TBL_AGENT ->
                    (dataContext != null) ? dataContext.getString(AIOpsObject.OBJECT_NAME) : ObjectConfigStore.getStore().getItemByAgentId(id) != null ? ObjectConfigStore.getStore().getItemByAgentId(id).getString(AIOpsObject.OBJECT_NAME) : entityName;

            case TBL_WIDGET ->
                    (dataContext != null) ? dataContext.getString(VisualizationConstants.VISUALIZATION_NAME) : WidgetConfigStore.getStore().getItem(id) != null ? WidgetConfigStore.getStore().getItem(id).getString(VisualizationConstants.VISUALIZATION_NAME) : entityName;

            case TBL_DASHBOARD ->
                    (dataContext != null) ? dataContext.getString(Dashboard.DASHBOARD_NAME) : DashboardConfigStore.getStore().getItem(id) != null ? DashboardConfigStore.getStore().getItem(id).getString(Dashboard.DASHBOARD_NAME) : entityName;

            case TBL_TEMPLATE ->
                    (dataContext != null) ? dataContext.getString(Template.TEMPLATE_NAME) : TemplateConfigStore.getStore().getItem(id) != null ? TemplateConfigStore.getStore().getItem(id).getString(Template.TEMPLATE_NAME) : entityName;

            case TBL_LOG_PARSER ->
                    (dataContext != null) ? dataContext.getString(LogParser.LOG_PARSER_NAME) : LogParserConfigStore.getStore().getItem(id) != null ? LogParserConfigStore.getStore().getItem(id).getString(LogParser.LOG_PARSER_NAME) : entityName;

            case TBL_LOG_PARSER_PLUGIN ->
                    (dataContext != null) ? dataContext.getString(LogParserPlugin.LOG_PARSER_PLUGIN_NAME) : LogParserConfigStore.getStore().getItem(id) != null ? LogParserConfigStore.getStore().getItem(id).getString(LogParserPlugin.LOG_PARSER_PLUGIN_NAME) : entityName;

            case TBL_FLOW_IP_GROUP ->
                    (dataContext != null) ? dataContext.getString(FlowIPGroup.FLOW_IP_GROUP_NAME) : FlowIPGroupConfigStore.getStore().getItem(id) != null ? FlowIPGroupConfigStore.getStore().getItem(id).getString(FlowIPGroup.FLOW_IP_GROUP_NAME) : entityName;

            case TBL_FLOW_AS_MAPPER ->
                    (dataContext != null) ? dataContext.getString(FlowASMapper.FLOW_AS_MAPPER_NAME) : FlowASMapperConfigStore.getStore().getItem(id) != null ? FlowASMapperConfigStore.getStore().getItem(id).getString(FlowASMapper.FLOW_AS_MAPPER_NAME) : entityName;

            case TBL_FLOW_DOMAIN_MAPPER ->
                    (dataContext != null) ? dataContext.getString(FlowDomainMapper.FLOW_DOMAIN_MAPPER_NAME) : FlowDomainMapperConfigStore.getStore().getItem(id) != null ? FlowDomainMapperConfigStore.getStore().getItem(id).getString(FlowDomainMapper.FLOW_DOMAIN_MAPPER_NAME) : entityName;

            case TBL_FLOW_GEOLOCATION_MAPPER ->
                    (dataContext != null) ? dataContext.getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_PROFILE_NAME) : FlowGeolocationMapperConfigStore.getStore().getItem(id) != null ? FlowGeolocationMapperConfigStore.getStore().getItem(id).getString(FlowGeolocationMapper.FLOW_GEOLOCATION_MAPPER_PROFILE_NAME) : entityName;

            case TBL_FLOW_IP_MAPPER ->
                    (dataContext != null) ? dataContext.getString(FlowIPMapper.FLOW_IP_MAPPER_PROFILE_NAME) : FlowIPMapperConfigStore.getStore().getItem(id) != null ? FlowIPMapperConfigStore.getStore().getItem(id).getString(FlowIPMapper.FLOW_IP_MAPPER_PROFILE_NAME) : entityName;

            case TBL_METRIC_POLICY ->
                    (dataContext != null) ? dataContext.getString(PolicyEngineConstants.POLICY_NAME) : MetricPolicyConfigStore.getStore().getItem(id) != null ? MetricPolicyConfigStore.getStore().getItem(id).getString(PolicyEngineConstants.POLICY_NAME) : entityName;

            case TBL_EVENT_POLICY ->
                    (dataContext != null) ? dataContext.getString(PolicyEngineConstants.POLICY_NAME) : EventPolicyConfigStore.getStore().getItem(id) != null ? EventPolicyConfigStore.getStore().getItem(id).getString(PolicyEngineConstants.POLICY_NAME) : entityName;

            case TBL_NETROUTE_POLICY ->
                    (dataContext != null) ? dataContext.getString(PolicyEngineConstants.POLICY_NAME) : NetRoutePolicyConfigStore.getStore().getItem(id) != null ? NetRoutePolicyConfigStore.getStore().getItem(id).getString(PolicyEngineConstants.POLICY_NAME) : entityName;

            case TBL_REPORT ->
                    (dataContext != null) ? dataContext.getString(Report.REPORT_NAME) : ReportConfigStore.getStore().getItem(id) != null ? ReportConfigStore.getStore().getItem(id).getString(Report.REPORT_NAME) : entityName;

            case TBL_EVENT_SOURCE ->
                    (dataContext != null) ? dataContext.getString(EVENT_SOURCE) : EventSourceConfigStore.getStore().getItem(id) != null ? EventSourceConfigStore.getStore().getItem(id).getString(EVENT_SOURCE) : entityName;

            case TBL_LOG_FORWARDER ->
                    (dataContext != null) ? dataContext.getString(LogForwarder.LOG_FORWARDER_NAME) : LogForwarderConfigStore.getStore().getItem(id) != null ? LogForwarderConfigStore.getStore().getItem(id).getString(LogForwarder.LOG_FORWARDER_NAME) : entityName;

            case TBL_LOG_COLLECTOR ->
                    (dataContext != null) ? dataContext.getString(LogCollector.LOG_COLLECTOR_NAME) : LogCollectorConfigStore.getStore().getItem(id) != null ? LogCollectorConfigStore.getStore().getItem(id).getString(LogCollector.LOG_COLLECTOR_NAME) : entityName;

            case TBL_REBRANDING,
                 TBL_MAIL_SERVER,
                 TBL_SMS_GATEWAY,
                 TBL_LDAP_SERVER,
                 TBL_PASSWORD_POLICY,
                 TBL_SNMP_TRAP_LISTENER,
                 TBL_FLOW_SAMPLING_RATE,
                 TBL_INTEGRATION,
                 TBL_TWO_FACTOR_AUTHENTICATION,
                 TBL_FLOW_SETTINGS -> EMPTY_VALUE;

            case TBL_SCHEDULER ->
                    (dataContext != null) ? dataContext.getString(Scheduler.SCHEDULER_JOB_TYPE) : SchedulerConfigStore.getStore().getItem(id) != null ? SchedulerConfigStore.getStore().getItem(id).getString(Scheduler.SCHEDULER_JOB_TYPE) : entityName;

            case TBL_TOPOLOGY_PLUGIN ->
                    (dataContext != null) ? dataContext.getString(TopologyPlugin.TOPOLOGY_PLUGIN_NAME) : TopologyPluginConfigStore.getStore().getItem(id) != null ? TopologyPluginConfigStore.getStore().getItem(id).getString(TopologyPlugin.TOPOLOGY_PLUGIN_NAME) : entityName;

            case TBL_RUNBOOK_PLUGIN ->
                    (dataContext != null) ? dataContext.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) : RunbookPluginConfigStore.getStore().getItem(id) != null ? RunbookPluginConfigStore.getStore().getItem(id).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) : entityName;

            case USER_PASSWORD_LAST_UPDATED_TIME, USER_TEMPORARY_PASSWORD_CREATED_TIME,
                 TagRule.TAG_RULE_TRIGGERED_TIME -> DateTimeUtil.getScheduledTimestamp(id);

            case Configuration.CONFIG_BACKUP_TIME, Configuration.CONFIG_LAST_BACKUP_TIME,
                 Configuration.CONFIG_LAST_DISCOVERY_TIME, Configuration.CONFIG_LAST_ACTION_TIME,
                 Configuration.CONFIG_LAST_UPGRADE_TIME ->
                    DateTimeUtil.getScheduledTimestamp(id * 1000); // we are storing time in seconds hence to keep flow generic it is multiplied with 1000

            case TBL_DEPENDENCY_MAPPER -> (dataContext != null) ?
                    (dataContext.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT) + "/"
                            + dataContext.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE)
                            + "-" + dataContext.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD) + "/"
                            + (dataContext.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).containsKey(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)
                            ? dataContext.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE) : ""))
                    : (DependencyMapperConfigStore.getStore().getItem(id).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT) + "/"
                    + DependencyMapperConfigStore.getStore().getItem(id).getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE)
                    + "-" + DependencyMapperConfigStore.getStore().getItem(id).getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD) + "/"
                    + (DependencyMapperConfigStore.getStore().getItem(id).getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).containsKey(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE)
                    ? DependencyMapperConfigStore.getStore().getItem(id).getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE) : ""));

            case PolicyEngineConstants.POLICY_CREATION_TIME -> DateTimeUtil.getScheduledTimestamp(id * 1000);

            case TBL_CONFIG_TEMPLATE ->
                    (dataContext != null) ? dataContext.getString(ConfigTemplate.CONFIG_TEMPLATE_NAME) : ConfigTemplateConfigStore.getStore().getItem(id) != null ? ConfigTemplateConfigStore.getStore().getItem(id).getString(ConfigTemplate.CONFIG_TEMPLATE_NAME) : entityName;

            case TBL_CONFIGURATION ->
                    (dataContext != null) ? dataContext.getString(AIOpsObject.OBJECT_NAME) : ObjectConfigStore.getStore().getItemByObjectId(ConfigurationConfigStore.getStore().getObjectId(id)) != null ? ObjectConfigStore.getStore().getItemByObjectId(ConfigurationConfigStore.getStore().getObjectId(id)).getString(AIOpsObject.OBJECT_NAME) : ArchivedObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, ConfigurationConfigStore.getStore().getObjectId(id)).getString(AIOpsObject.OBJECT_NAME);

            case TBL_STORAGE_PROFILE, Configuration.CONFIG_STORAGE_PROFILE ->
                    (dataContext != null) ? dataContext.getString(StorageProfile.STORAGE_PROFILE_NAME) : StorageProfileConfigStore.getStore().getItem(id) != null ? StorageProfileConfigStore.getStore().getItem(id).getString(StorageProfile.STORAGE_PROFILE_NAME) : entityName;

            case TBL_BACKUP_PROFILE ->
                    (dataContext != null) ? dataContext.getString(BackupProfile.BACKUP_PROFILE_NAME) : BackupProfileConfigStore.getStore().getItem(id) != null ? BackupProfileConfigStore.getStore().getItem(id).getString(BackupProfile.BACKUP_PROFILE_NAME) : entityName;

            case TBL_PERSONAL_ACCESS_TOKEN ->
                    (dataContext != null) ? dataContext.getString(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_NAME) : PersonalAccessTokenConfigStore.getStore().getItem(id) != null ? PersonalAccessTokenConfigStore.getStore().getItem(id).getString(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_NAME) : entityName;

            case TBL_COMPLIANCE_RULE ->
                    (dataContext != null) ? dataContext.getString(ComplianceRule.COMPLIANCE_RULE_NAME) : ComplianceRuleConfigStore.getStore().getItem(id) != null ? ComplianceRuleConfigStore.getStore().getItem(id).getString(ComplianceRule.COMPLIANCE_RULE_NAME) : entityName;

            case TBL_COMPLIANCE_BENCHMARK ->
                    (dataContext != null) ? dataContext.getString(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME) : ComplianceBenchmarkConfigStore.getStore().getItem(id) != null ? ComplianceBenchmarkConfigStore.getStore().getItem(id).getString(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME) : entityName;

            case TBL_COMPLIANCE_POLICY ->
                    (dataContext != null) ? dataContext.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME) : CompliancePolicyConfigStore.getStore().getItem(id) != null ? CompliancePolicyConfigStore.getStore().getItem(id).getString(CompliancePolicy.COMPLIANCE_POLICY_NAME) : entityName;

            case TBL_TAG_RULE ->
                    (dataContext != null) ? dataContext.getString(TagRule.TAG_RULE_NAME) : TagRuleConfigStore.getStore().getItem(id) != null ? TagRuleConfigStore.getStore().getItem(id).getString(TagRule.TAG_RULE_NAME) : entityName;

            case TBL_NETROUTE ->
                    (dataContext != null) ? dataContext.getString(NetRoute.NETROUTE_NAME) : NetRouteConfigStore.getStore().getItem(id) != null ? NetRouteConfigStore.getStore().getItem(id).getString(NetRoute.NETROUTE_NAME) : entityName;

            case TBL_DNS_SERVER_PROFILE ->
                    (dataContext != null) ? dataContext.getString(DNSServerProfile.DNS_SERVER_IP) : DNSServerProfileConfigStore.getStore().getItem(id) != null ? DNSServerProfileConfigStore.getStore().getItem(id).getString(DNSServerProfile.DNS_SERVER_IP) : entityName;

            default -> CommonUtil.getString(id);
        };
    }

    private void auditCustomFieldChanges(String entity, String prop, JsonObject oldProps, JsonObject newProps, JsonObject previousContext, JsonObject updatedContext)
    {
        if (entity.equalsIgnoreCase(Entity.AGENT.getName()) && prop.equalsIgnoreCase(Agent.AGENT_CONFIGS))
        {
            normalize(entity, new JsonObject(oldProps.getString(prop)), new JsonObject(newProps.getString(prop)), previousContext, updatedContext, mapFieldTitles(CommonUtil.getEntitySchema("motadata-agent")));
        }

        else if (entity.equalsIgnoreCase(Entity.OBJECT.getName()) && prop.equalsIgnoreCase(AIOpsObject.OBJECT_CUSTOM_FIELDS) && newProps.getJsonObject(prop) != null && oldProps.getJsonObject(prop) != null)
        {
            var oldCustomProps = new JsonObject();

            var newCustomProps = new JsonObject();

            Stream.concat(oldProps.getJsonObject(prop).getMap().keySet().stream(), newProps.getJsonObject(prop).getMap().keySet().stream()).collect(Collectors.toSet()).forEach(key ->
            {
                ((JsonObject) oldCustomProps.getMap().computeIfAbsent(prop, value -> new JsonObject())).put(CustomMonitoringFieldConfigStore.getStore().getItem(Long.parseLong(key)).getString(CUSTOM_MONITORING_FIELD_NAME), oldProps.getJsonObject(prop) != null && oldProps.getJsonObject(prop).getValue(key) != null ? oldProps.getJsonObject(prop).getValue(key) : EMPTY_VALUE);

                ((JsonObject) newCustomProps.getMap().computeIfAbsent(prop, value -> new JsonObject())).put(CustomMonitoringFieldConfigStore.getStore().getItem(Long.parseLong(key)).getString(CUSTOM_MONITORING_FIELD_NAME), newProps.getJsonObject(prop) != null && newProps.getJsonObject(prop).getValue(key) != null ? newProps.getJsonObject(prop).getValue(key) : EMPTY_VALUE);
            });

            normalize(entity, new JsonObject(oldCustomProps.getString(prop)), new JsonObject(newCustomProps.getString(prop)), previousContext, updatedContext, mapFieldTitles(CommonUtil.getEntitySchema("objects")));
        }
    }

    private void normalize(String entity, JsonObject oldProps, JsonObject newProps, JsonObject previousContext, JsonObject updatedContext, JsonObject fields)
    {
        if (newProps != null)
        {
            newProps.getMap().keySet().stream().filter(key -> oldProps.containsKey(key) && !newProps.getValue(key).equals(oldProps.getValue(key)) && (PASSOVER_FIELDS_BY_ENTITY.get(entity) == null || !PASSOVER_FIELDS_BY_ENTITY.get(entity).contains(key))).forEach(key ->
            {
                if (oldProps.getValue(key) instanceof JsonObject && newProps.getValue(key) instanceof JsonObject)
                {
                    normalize(entity, oldProps.getJsonObject(key), newProps.getJsonObject(key), previousContext, updatedContext, fields);
                }

                else
                {
                    previousContext.put(fields != null && fields.getString(key) != null ? fields.getString(key) : key, getUpdatedEntityName(oldProps.getValue(key, EMPTY_VALUE), key));

                    updatedContext.put(fields != null && fields.getString(key) != null ? fields.getString(key) : key, getUpdatedEntityName(newProps.getValue(key), key));
                }
            });
        }
    }
}
