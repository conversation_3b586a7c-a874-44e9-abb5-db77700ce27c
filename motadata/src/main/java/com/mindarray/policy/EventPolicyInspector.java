/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date          Author              Notes
 *   2025-02-04    Smit Prajapati      MOTADATA-4954  Alert Sound Notification for Event Policy
 *   2025-02-11    Chandresh           MOTADATA-446   Event Policy Template related enhancements
 *   2025-02-25	   Darshan Parmar	   MOTADATA-5215  SonarQube Suggestions Resolution
 *   2025-02-27    Chopra Deven        MOTADATA-4973   Dumping Event source for Event Policy in case of notification trigger action event and policy trigger event
 *   2025-02-28    Smit Prajapati      MOTADATA-4956   Added try-cache block for exception handling
 *   2025-03-25    Smit Prajapati      MOTADATA-5435  Flow back-pressure mechanism.
 *   9-Apr-2025    Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *   2-Jun-2025    Smit Prajapati      MOTADATA-6418: EventPolicyInspector/EventPolicyAggregator Support
 * * 03-July-2025  Smit Prajapati      MOTADATA-6540Verified consolidations verticles major modules.
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.notification.Notification;
import com.mindarray.store.EventPolicyCacheStore;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.EventPolicy.*;
import static com.mindarray.api.NetRoute.NETROUTE_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * EventPolicyInspector is the core component responsible for evaluating event policies
 * and triggering appropriate actions based on the processed event data.
 * <p>
 * It receives pre-aggregated group statistics from EventPolicyInspector, evaluates them
 * against defined policy conditions (e.g., threshold, range), and takes configured actions
 * such as notifications, suppression, or runbook execution.
 * <p>
 * Additionally, it manages the lifecycle of policies — including scheduling, suppression windows,
 * evaluation intervals, and interaction with other components like the visualization engine.
 * <p>
 * Key responsibilities include:
 * <ul>
 *   <li>Receiving group statistics from EventPolicyAggregator</li>
 *   <li>Evaluating policy conditions (sum, avg, count, range, etc.)</li>
 *   <li>Triggering actions: notifications, sound alerts, runbooks, etc.</li>
 *   <li>Handling policy lifecycle events: add, update, delete, disable</li>
 *   <li>Supporting scheduled evaluations and integrating with visualization context</li>
 * </ul>
 * <p>
 * This class represents the policy **execution and decision-making engine**.
 */

public class EventPolicyInspector extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(EventPolicyInspector.class, GlobalConstants.MOTADATA_POLICY, "Event Policy Inspector");
    /**
     * Delivery options for event bus communications with 5-minute timeout
     */
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(300000L);

    /**
     * Interval in seconds for periodic policy inspection
     */
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getEventPolicyInspectionTimerSeconds();

    /**
     * Maximum number of top groups to process in group-based policies
     */
    private static final int TOP_N_GROUPS = MotadataConfigUtil.getEventPolicyTopNGroups();

    /**
     * Local host name/address for event source identification
     */
    private static final String LOCAL_HOST = MotadataConfigUtil.getHost();
    /**
     * Map of policy IDs to their context information
     */
    private final Map<Long, JsonObject> policies = new HashMap<>(64);

    /**
     * Storage for event column mappings
     */
    private final JsonObject eventColumns = new JsonObject();

    /**
     * Set of policy IDs with disabled action triggers
     */
    private final Set<Long> disabledPolicyActionTriggers = new HashSet<>();

    /**
     * Map of policy IDs to their grouping counter columns (for policies with group-by clauses)
     */
    private final Map<Long, String[]> groupingCounters = new HashMap<>();

    /**
     * Reusable JsonObject for record construction
     */
    private final JsonObject record = new JsonObject();

    /**
     * Collection of records for policy evaluation results
     */
    private final JsonArray records = new JsonArray();

    /**
     * Map of policy IDs to their evaluation intervals
     */
    private final Map<Long, int[]> evaluatedPolicies = new HashMap<>();

    /**
     * StringBuilder for efficient string operations with initial capacity
     */
    private final StringBuilder builder = new StringBuilder(1024);
    /**
     * Cache for frequently constructed field keys
     */
    private final Map<String, String> cacheFields = new HashMap<>(32);
    /**
     * Map of policy IDs to their group statistics
     */
    private final Map<Long, JsonObject> policyGroupStats = new HashMap<>();
    private final Map<Long, Long> policyEvaluationTicks = new HashMap<>(64);
    /**
     * Set of mapper names
     */
    private Set<String> mappers;

    /**
     * Initializes the EventPolicyInspector verticle.
     * <p>
     * This method sets up event bus consumers, periodic timers, and initializes the event engine.
     * It loads existing policies from the store and prepares them for evaluation.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var policies = EventPolicyConfigStore.getStore().getItems();

        if (policies != null)
        {
            for (var index = 0; index < policies.size(); index++)
            {
                var policy = policies.getJsonObject(index);

                if (YES.equalsIgnoreCase(policy.getString(POLICY_STATE)))
                {
                    add(policy);
                }
            }
        }
        mappers = new HashSet<>();

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

            try
            {
                var items = EventPolicyConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO))
                    {
                        this.policies.put(policy.getLong(ID), enrich(policy));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                {
                    update(eventColumns, tokens, false);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
        {
            for (var entry : evaluatedPolicies.entrySet())
            {
                var policyId = entry.getKey();

                var values = entry.getValue(); // times[0] = countdown, times[1] = original

                values[0] -= INTERVAL_SECONDS;

                if (values[0] <= 0)
                {
                    if(CommonUtil.debugEnabled())
                    {
                        LOGGER.debug("Requesting to get data from instances, for Policy:  " + this.policies.get(policyId).getString(POLICY_NAME) + " : " + policyEvaluationTicks.get(policyId) + " evaluated-polices: " + Arrays.toString(evaluatedPolicies.get(policyId)));
                    }

                    fetch(policyId, UNKNOWN);

                    values[0] = values[1]; // reset countdown to original
                }
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {

                    case ADD_POLICY ->
                    {
                        var item = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        this.policies.put(item.getLong(ID), enrich(item));

                        disabledPolicyActionTriggers.remove(event.getLong(ID));

                        init(item, item.getLong(ID));
                    }

                    case UPDATE_POLICY ->
                    {
                        var item = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        this.policies.put(item.getLong(ID), enrich(item));

                        init(item, item.getLong(ID));
                    }

                    case DELETE_POLICY ->
                    {
                        this.policies.remove(event.getLong(ID));

                        disabledPolicyActionTriggers.remove(event.getLong(ID));

                        clear(event.getLong(ID));
                    }

                    case DISABLE_POLICY_ACTION_TRIGGER ->
                    {
                        disabledPolicyActionTriggers.remove(event.getLong(POLICY_ID));

                        var item = SchedulerConfigStore.getStore().getItem(event.getLong(ID));

                        if (item != null)
                        {
                            Bootstrap.configDBService().delete(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    SchedulerConfigStore.getStore().deleteItem(event.getLong(ID));

                                    JobScheduler.removeJob(event.getLong(ID));
                                }
                            });
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_EVENT_POLICY_INSPECT, message ->
        {

            var event = message.body();

            if (event.containsKey(RESULT) || event.containsKey("schedule"))
            {
                inspect(event, event.getLong(POLICY_ID));
            }
            else
            {
                inspect(event);
            }
        });

        //All EventPolicyProcessor will send its stats to manager...
        vertx.eventBus().<JsonObject>localConsumer(EVENT_EVENT_POLICY_AGGREGATOR_RESPONSE, message ->
        {

            var id = message.body().getLong(ID);

            var groupStats = policyGroupStats.get(id);

            if (groupStats != null && !groupStats.isEmpty())
            {
                merge(message.body().getJsonObject(RESULT), groupStats);
            }
            else
            {
                policyGroupStats.put(id, message.body().getJsonObject(RESULT));
            }

        });

        //fOR Testing only...
        vertx.eventBus().<Long>localConsumer(EVENT_EVENT_POLICY_TEST, message -> fetch(message.body(), "test"));

        promise.complete();
    }

    private void fetch(Long policyId, String type)
    {
        var policy = policies.get(policyId);

        var count = MotadataConfigUtil.getLogParsers();

        var eventType = EVENT_LOG;

        if(policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FLOW.getName()))
        {
            count = MotadataConfigUtil.getFlowProcessors();

            eventType = EVENT_FLOW;
        }

        var futures = new ArrayList<Future<Void>>();

        //for testing when type is not unknown we are fetching data from the test
        if(!UNKNOWN.equalsIgnoreCase(type))
        {
            eventType = type;

            count = 1;
        }

        for(var index = 0; index < count; index++)
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            vertx.eventBus().<JsonObject>request(EVENT_EVENT_POLICY_AGGREGATOR_QUERY + DOT_SEPARATOR + eventType ,policyId,message ->{

                try
                {
                    var id = message.result().body().getLong(ID);

                    var groupStats = policyGroupStats.get(id);

                    if (groupStats != null && !groupStats.isEmpty())
                    {
                        merge(message.result().body().getJsonObject(RESULT), groupStats);
                    }
                    else
                    {
                        policyGroupStats.put(id, message.result().body().getJsonObject(RESULT));
                    }

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }

        Future.join(futures).onComplete(result ->{

            if(result.succeeded())
            {
                if(CommonUtil.debugEnabled())
                {
                    LOGGER.debug("Data collected from all instances, going for the inspect. Policy: " + policy.getString(POLICY_NAME) + ". tick: " + policyEvaluationTicks.get(policyId) + " data: " + policyGroupStats.get(policyId));
                }

                inspect(new JsonObject().put("inspect", YES).put(POLICY_ID, policyId));
            }
            else
            {
                LOGGER.info("Failed to collect data from instances for policy: " + policy.getString(POLICY_NAME));
            }
        });
    }

    /**
     * Merges the incoming group statistics with existing policy group statistics.
     * Updates the count and sum values for each group accordingly.
     *
     * @param items New statistics to merge
     */
    private void merge(JsonObject items, JsonObject groupStats)
    {
        try
        {
            for (var item : items)
            {
                var stats = groupStats.getJsonArray(item.getKey());

                var values = (JsonArray) item.getValue();

                if (stats != null)
                {
                    stats.set(0, stats.getLong(0) + values.getLong(0));

                    stats.set(1, stats.getLong(1) + values.getLong(1));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    /**
     * Initializes or reinitializes a specific policy.
     * <p>
     * This method is called when a policy is added or updated to prepare
     * the data structures needed for its evaluation.
     *
     * @param policy   The policy configuration
     * @param policyId The ID of the policy
     */
    private void init(JsonObject policy, long policyId)
    {
        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
        {
            groupingCounters.remove(policyId);
        }

        evaluatedPolicies.remove(policyId);

        policyEvaluationTicks.remove(policyId);

        add(policy);
    }

    /**
     * Clears all data structures associated with a policy.
     * <p>
     * This method is called when a policy is deleted to clean up any resources
     * associated with it.
     *
     * @param policyId The ID of the policy to clear
     */
    private void clear(long policyId)
    {
        groupingCounters.remove(policyId);

        evaluatedPolicies.remove(policyId);

        policyEvaluationTicks.remove(policyId);
    }

    /**
     * Inspects an event against in-memory policies.
     * <p>
     * This method evaluates an incoming event against applicable policies stored in memory.
     * It handles different types of events including traps and regular events, and manages
     * the policy evaluation lifecycle.
     *
     * @param event The event to inspect
     */
    private void inspect(JsonObject event) // inspect for in-memory policies
    {
        try
        {

            var policy = policies.get(event.getLong(POLICY_ID));

            if (policy != null)
            {
                var context = policy.getJsonObject(POLICY_CONTEXT);

                if (!event.containsKey(EVENT_TIMESTAMP))
                {
                    event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (event.containsKey(EVENT) && EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT))) //trap policy
                {
                    event.put(EVENT_TIMESTAMP, CommonUtil.getLong(event.getValue(EVENT_TIMESTAMP)));

                    records.clear();

                    record.clear();

                    if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), event.getString(context.getString(VisualizationConstants.DATA_POINT))))
                    {
                        if (YES.equalsIgnoreCase(policy.getString(POLICY_CLEAR_STATE, NO)) && PolicyEngineConstants.filterTrap(context.getJsonObject(POLICY_CLEAR_FILTERS).getJsonObject(DATA_FILTER, null), event))
                        {
                            //update severity as CLEAR severity and removing unnecessary fields...
                            event.remove(SNMPTrapProcessor.SNMP_TRAP_VARIABLES);

                            records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + DatastoreConstants.AggregationType.LAST, event));

                            trigger(policy, context, event, event.getLong(EVENT_TIMESTAMP), Severity.CLEAR.name());
                        }
                        else
                        {
                            //removing unnecessary fields...
                            event.remove(SNMPTrapProcessor.SNMP_TRAP_VARIABLES);

                            records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + DatastoreConstants.AggregationType.LAST, event));

                            trigger(policy, context, event, event.getLong(EVENT_TIMESTAMP), context.getString(POLICY_SEVERITY));
                        }
                    }
                }
                else
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("Policy: [%s], going for inspect", policy.getString(POLICY_NAME)));
                    }

                    records.clear();

                    record.clear();

                    var groupStats = policyGroupStats.get(policy.getLong(ID));

                    if (groupStats != null && !groupStats.isEmpty())
                    {
                        if (context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                        {
                            var aggregator = context.getString(VisualizationConstants.AGGREGATOR);

                            var stats = sort(groupStats, aggregator);

                            for (var i = 0; i < Math.min(TOP_N_GROUPS, stats.size()); i++)
                            {
                                var values = (JsonArray) stats.get(i).getValue();

                                evaluate(policy, context, DatastoreConstants.AggregationType.SUM.getName().equalsIgnoreCase(aggregator) ? values.getLong(1) : (DatastoreConstants.AggregationType.AVG.getName().equalsIgnoreCase(aggregator) ? (values.getLong(0) == 0 ? 0 : values.getLong(1) / values.getLong(0)) : values.getLong(0)), stats.get(i).getKey());
                            }

                            if (!records.isEmpty())
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug(policy.getString(POLICY_NAME) + " --> " + records.encode());
                                }

                                trigger(policy, context, event, policyEvaluationTicks.remove(policy.getLong(ID)), context.getString(POLICY_SEVERITY));

                                policyEvaluationTicks.put(policy.getLong(ID), DateTimeUtil.currentSeconds());
                            }
                        }
                        else
                        {
                            var stats = policyGroupStats.get(policy.getLong(ID));

                            var aggregator = context.getString(VisualizationConstants.AGGREGATOR);

                            long value;

                            var values = stats.getJsonArray("default");

                            if (DatastoreConstants.AggregationType.SUM.getName().equalsIgnoreCase(aggregator))
                            {
                                value = values.getLong(1);

                            }
                            else if (DatastoreConstants.AggregationType.AVG.getName().equalsIgnoreCase(aggregator))
                            {
                                value = values.getLong(0) == 0 ? 0 : values.getLong(1) / values.getLong(0);
                            }
                            else
                            {
                                value = values.getLong(0);
                            }

                            if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), value))
                            {
                                records.add(record.put(context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + aggregator, value));

                                LOGGER.info("triggering... " + policy.getString(POLICY_NAME) + " : " + policyEvaluationTicks.get(policy.getLong(ID)) + " evaluated-polices: " + Arrays.toString(evaluatedPolicies.get(policy.getLong(ID))));

                                trigger(policy, context, event, policyEvaluationTicks.remove(policy.getLong(ID)), context.getString(POLICY_SEVERITY));

                                policyEvaluationTicks.put(policy.getLong(ID), DateTimeUtil.currentSeconds());
                            }
                        }

                        // Reset state
                        groupStats.clear();
                    }
                }

                policyEvaluationTicks.put(policy.getLong(ID), DateTimeUtil.currentSeconds());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Inspects an event against a scheduled policy.
     * <p>
     * This method evaluates an event against a specific scheduled policy identified by its ID.
     * It handles visualization results and scheduled policy context preparation.
     *
     * @param event    The event to inspect
     * @param policyId The ID of the policy to evaluate against
     */
    private void inspect(JsonObject event, long policyId) // inspect for scheduled policies (db events)
    {
        try
        {
            var policy = policies.get(policyId);

            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (!event.containsKey(EVENT_TIMESTAMP))
            {
                event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
            }

            if (event.containsKey(RESULT)) //visualization response of scheduled policy
            {
                records.clear();

                var rows = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(RESULT)), LOGGER, false, null, true, true).getJsonArray(RESULT);

                if (rows != null && !rows.isEmpty())
                {
                    var fieldKey = context.getString(VisualizationConstants.DATA_POINT) + CARET_SEPARATOR + context.getString(VisualizationConstants.AGGREGATOR);

                    for (var index = 0; index < rows.size(); index++)
                    {
                        var row = rows.getJsonObject(index);

                        if (row.containsKey(fieldKey) && PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), row.getValue(fieldKey)))
                        {
                            records.add(row);
                        }
                    }

                    if (!records.isEmpty())
                    {
                        trigger(policy, context, event, DUMMY_ID, context.getString(POLICY_SEVERITY));
                    }
                }
            }
            else if (event.containsKey("schedule")) // prepare context for scheduled policy
            {
                prepareContext(policy.getLong(ID), policy);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Evaluates a policy condition for a specific group.
     * <p>
     * This method checks if a value from a group meets the policy condition and,
     * if so, adds a record to the results.
     *
     * @param policy   The policy configuration
     * @param context  The policy context
     * @param value    The value to evaluate
     * @param grouping The group identifier
     */
    private void evaluate(JsonObject policy, JsonObject context, Object value, String grouping)
    {
        // Skip evaluation if value is null
        if (value == null)
        {
            return;
        }

        if (PolicyEngineConstants.evaluateCondition(true, context.getString(OPERATOR), context.getValue(VALUE), value))
        {
            var record = new JsonObject();

            var policyId = policy.getLong(ID);

            var dataPoint = context.getString(VisualizationConstants.DATA_POINT);

            var aggregator = context.getString(VisualizationConstants.AGGREGATOR);

            // Use cached field key
            record.put(getFieldKey(dataPoint, aggregator), value);

            // Get grouping counters once
            var counters = groupingCounters.get(policyId);

            // Safety check
            if (counters == null)
            {
                return;
            }

            if (grouping.contains(COLUMN_SEPARATOR))
            {
                var values = grouping.split(COLUMN_SEPARATOR);

                var length = Math.min(values.length, counters.length);

                for (var j = 0; j < length; j++)
                {
                    record.put(counters[j], values[j]);
                }
            }
            else if (counters.length > 0)
            {
                record.put(counters[0], grouping);
            }

            records.add(record);
        }
    }


    /**
     * Prepares the context for a scheduled policy evaluation.
     * <p>
     * This method constructs the visualization context needed for evaluating
     * a scheduled policy and sends it to the visualization event bus.
     *
     * @param policyId The ID of the policy
     * @param policy   The policy configuration
     */
    private void prepareContext(long policyId, JsonObject policy)
    {
        JsonObject context;

        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
        {
            context = new JsonObject(VisualizationConstants.VISUALIZATION_TOP_N_CONTEXT.replace("@@@", policyContext.getString(VisualizationConstants.DATA_POINT) + "." + policyContext.getString(VisualizationConstants.AGGREGATOR)));
        }
        else
        {
            context = new JsonObject(VisualizationConstants.VISUALIZATION_GAUGE_CONTEXT);
        }

        var dataSource = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);

        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.LOG.getName()))
        {
            dataSource.put(VisualizationConstants.TYPE, VisualizationConstants.VisualizationDataSource.LOG.getName());

            dataSource.put(VisualizationConstants.CATEGORY, VisualizationConstants.VisualizationDataSource.LOG.getName());
        }
        else
        {
            dataSource.put(VisualizationConstants.TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName());

            dataSource.put(VisualizationConstants.CATEGORY, VisualizationConstants.VisualizationDataSource.FLOW.getName());
        }

        if (policyContext.containsKey(GlobalConstants.FILTERS))
        {
            dataSource.put(GlobalConstants.FILTERS, policyContext.getJsonObject(GlobalConstants.FILTERS));
        }

        if (policyContext.containsKey(POLICY_RESULT_BY))
        {
            dataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, policyContext.getJsonArray(POLICY_RESULT_BY));
        }

        var dataPoint = new JsonObject().put(VisualizationConstants.AGGREGATOR, policyContext.getString(VisualizationConstants.AGGREGATOR)).put(VisualizationConstants.DATA_POINT, policyContext.getString(VisualizationConstants.DATA_POINT));

        if (!policyContext.getJsonArray(ENTITIES).isEmpty())
        {
            dataPoint.put(ENTITIES, policyContext.getJsonArray(ENTITIES));
        }

        if (policyContext.containsKey(ENTITY_TYPE) && !policyContext.getString(ENTITY_TYPE).isEmpty())
        {
            dataPoint.put(ENTITY_TYPE, policyContext.getString(ENTITY_TYPE));
        }

        if (policyContext.getValue(EVALUATION_WINDOW) != null)
        {
            String timeline;

            if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Daily"))
            {
                timeline = "-1d";
            }
            else if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Weekly"))
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_7_DAYS.getName();
            }
            else if (policy.getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("Monthly"))
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_30_DAYS.getName();
            }
            else
            {
                timeline = VisualizationConstants.VisualizationTimeline.LAST_1_HOUR.getName();
            }

            context.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).put(VisualizationConstants.RELATIVE_TIMELINE, timeline);
        }

        dataSource.put(VisualizationConstants.DATA_POINTS, new JsonArray().add(dataPoint));

        vertx.eventBus().send(EVENT_VISUALIZATION, context.put(PolicyEngineConstants.POLICY_ID, policyId).put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_EVENT_POLICY_INSPECT));
    }

    /**
     * Triggers actions when a policy condition is met.
     * <p>
     * This method is called when a policy evaluation determines that the condition
     * has been met. It records the event, sends notifications, and triggers configured
     * actions based on the policy configuration.
     *
     * @param policy   The policy configuration
     * @param context  The policy context
     * @param event    The event that triggered the policy
     * @param tick     The timestamp of the evaluation
     * @param severity The severity level of the triggered policy
     */
    private void trigger(JsonObject policy, JsonObject context, JsonObject event, long tick, String severity)
    {
        try
        {
            if (YES.equalsIgnoreCase(policy.getString(POLICY_STATE)))
            {
                EventPolicyCacheStore.getStore().updateTriggerTicks(policy.getLong(ID), event.getLong(EVENT_TIMESTAMP));

                if (!event.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                {
                    event.put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(VisualizationConstants.FROM_DATETIME, tick * 1000).put(VisualizationConstants.TO_DATETIME, (evaluatedPolicies.get(policy.getLong(ID)) == null ? DateTimeUtil.currentSeconds() : tick + evaluatedPolicies.get(policy.getLong(ID))[1]) * 1000));
                }

                var id = CommonUtil.newEventId();

                var message = context.getString(VisualizationConstants.DATA_POINT) + " " + context.getString(VisualizationConstants.AGGREGATOR, EMPTY_VALUE) + " " + PolicyEngineConstants.toOperatorLiteral(context.getString(OPERATOR)) + " " + context.getValue(VALUE);

                if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
                {
                    message = context.getString(VisualizationConstants.DATA_POINT) + " " + context.getString(VisualizationConstants.AGGREGATOR, EMPTY_VALUE) + " " + PolicyEngineConstants.toOperatorLiteral(context.getString(OPERATOR)) + " " + context.getJsonArray(VALUE).getString(0) + " and " + context.getJsonArray(VALUE).getString(1);
                }

                var notificationContext = new JsonObject().put(EventPolicy.TRIGGER_MODE, context.getString(EventPolicy.TRIGGER_MODE)).put(EventBusConstants.EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(SEVERITY, severity.toLowerCase()).put(EVENT_FIELD, context.getString(VisualizationConstants.DATA_POINT)).put(VALUE, records.size()).put(EVALUATION_WINDOW, DateTimeUtil.getDateTime(event.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE))).put(TRIGGERED_VALUE, context.getValue(VALUE));

                DatastoreConstants.write(new JsonObject()
                        .put(SEVERITY, severity)
                        .put(ID, id)
                        .put(MESSAGE, message).put(NETROUTE_ID, 0)
                        .put(POLICY_TYPE, policy.getString(POLICY_TYPE))//TODO Need to change type as per PMG
                        .put(POLICY_ID, policy.getLong(ID)).put(EVENT_FIELD, context.getString(VisualizationConstants.DATA_POINT))
                        .put(GlobalConstants.PLUGIN_ID, policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FLOW.getName()) ? DatastoreConstants.PluginId.POLICY_FLOW.getName() : policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.TRAP.getName()) ? DatastoreConstants.PluginId.POLICY_TRAP.getName() : DatastoreConstants.PluginId.POLICY_EVENT.getName())
                        .put(EventBusConstants.EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY.ordinal()).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY.getName(), mappers, builder);

                DatastoreConstants.write(new JsonObject()
                        .put(POLICY_TRIGGER_ID, id).put(SEVERITY, severity).put(POLICY_TRIGGER_EVALUATION_WINDOW, event.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).encode())
                        .put(POLICY_TRIGGER_VALUE, new JsonObject().put(RESULT, records).encode())
                        .put(POLICY_TRIGGER_POLICY_ID, policy.getLong(ID)).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.POLICY_RESULT.getName())
                        .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal()).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY_RESULT.getName(), mappers, builder);

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION,
                        new JsonObject().put(POLICY_NAME, policy.getString(POLICY_NAME))
                                .put(POLICY_TYPE, policy.getString(POLICY_TYPE))
                                .put(POLICY_ID, policy.getString(ID))
                                .put(SEVERITY, severity)
                                .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                .put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : EMPTY_VALUE)
                                .mergeIn(EventPolicyCacheStore.getStore().getTriggerTicks(policy.getLong(ID))));

                if (policy.getString(POLICY_SUPPRESS_ACTION, NO).equalsIgnoreCase(YES) && !disabledPolicyActionTriggers.contains(policy.getLong(ID)))
                {
                    disabledPolicyActionTriggers.add(policy.getLong(ID));

                    //on policy suppression once notification is sent after that it is suppressed for particular selected time
                    triggerAction(severity, policy, event, message, notificationContext);

                    var time = policy.getLong(POLICY_SUPPRESS_WINDOW);

                    if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.MINUTE))
                    {
                        time *= 60;
                    }
                    else if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.HOUR))
                    {
                        time *= 60 * 60;
                    }
                    else if (policy.getString(POLICY_SUPPRESS_WINDOW_UNIT).equalsIgnoreCase(DateTimeUtil.DAY))
                    {
                        time *= 60 * 60 * 24;
                    }

                    var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (time * 1000L));

                    Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, new JsonObject().put(POLICY_ID, policy.getLong(ID)).put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1])).put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0]).put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.POLICY_ACTION_TRIGGER_DISABLE.getName()).put(Scheduler.SCHEDULER_STATE, YES).put(Scheduler.SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            SchedulerConfigStore.getStore().addItem(asyncResult.result()).onComplete(result -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(asyncResult.result())));

                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_POLICY_ACTION).put(MESSAGE, String.format(InfoMessageConstants.POLICY_SUPPRESSED, policy.getString(POLICY_NAME), DEFAULT_USER, dateTime)));
                        }
                    });
                }

                if (!disabledPolicyActionTriggers.contains(policy.getLong(ID)))
                {
                    triggerAction(severity, policy, event.put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)), message, notificationContext);
                }
            }
            else
            {
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Policy: [%s], Not triggered. Reason: Policy Disabled", policy.getString(POLICY_NAME)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Triggers specific actions based on policy configuration.
     * <p>
     * This method handles the execution of configured actions when a policy is triggered,
     * including sending notifications via email, SMS, sound alerts, and integration channels,
     * as well as executing runbooks.
     *
     * @param severity The severity level of the triggered policy
     * @param policy   The policy configuration
     * @param event    The event that triggered the policy
     * @param message  The message to include in notifications
     * @param context  The context information for the triggered policy
     */
    private void triggerAction(String severity, JsonObject policy, JsonObject event, String message, JsonObject context)
    {
        var actions = policy.getJsonObject(POLICY_ACTIONS);

        if (actions != null && actions.containsKey(PolicyTriggerActionType.NOTIFICATION.getName())
                && actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()) != null
                && !actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty())
        {
            var notificationContext = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName());

            context.put(POLICY_URL, String.format(POLICY_URL_VALUE, Base64.getEncoder().encodeToString(new JsonObject().put(POLICY_DRILL_DOWN_TEMPLATE, YES).put(PolicyEngineConstants.POLICY_ID, policy.getLong(ID, 0L)).put(EVENT_SOURCE, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST).put(METRIC, policy.getString(METRIC, EMPTY_VALUE)).put(POLICY_TYPE, policy.getString(POLICY_TYPE, EMPTY_VALUE)).encode().getBytes())));

            if (notificationContext.containsKey(Notification.NotificationType.EMAIL.getName())
                    && !notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).isEmpty())
            {
                var emailRecipients = new HashSet<String>();

                var smsRecipients = new HashSet<String>();

                PolicyEngineConstants.setRecipients(emailRecipients, smsRecipients, notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).getJsonArray(severity));

                if (!emailRecipients.isEmpty())
                {
                    var recipients = new JsonArray(new ArrayList<>(emailRecipients));

                    var subject = EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_EMAIL_SUBJECT.replace("$$$severity$$$", severity), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_EMAIL_SUBJECT.replace("$$$severity$$$", severity), message);

                    // MOTADATA-3974
                    event.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS, recipients);

                    event.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT, subject);

                    event.put(SEVERITY, severity.toLowerCase());

                    context.mergeIn(policy);

                    if (policy.containsKey(POLICY_MESSAGE) && !policy.getString(POLICY_MESSAGE).isEmpty())
                    {
                        context.put(POLICY_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message));
                    }
                    else
                    {
                        context.put(POLICY_MESSAGE, message);
                    }

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                            .put(Notification.TEMPLATE_NAME, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? Notification.EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE : Notification.EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, context.put(EVENT_SOURCE, event.getString(EVENT_SOURCE, EMPTY_VALUE)).put(MESSAGE, message).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(event.put(RESULT, new JsonArray().addAll(records)), policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });

                    if (MotadataConfigUtil.devMode())
                    {
                        Notification.sendEmail(new JsonObject()
                                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                                .put(Notification.TEMPLATE_NAME, PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)) ? Notification.EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE : Notification.EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE)
                                .put(Notification.EMAIL_NOTIFICATION_CONTENT, context.put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).getMap()));
                    }

                }

                if (!smsRecipients.isEmpty())
                {

                    vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, new JsonArray(new ArrayList<>(smsRecipients)))
                            .put(Notification.SMS_NOTIFICATION_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_SMS, message, event) : replaceEventPolicyPlaceholders(policy, event, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_EVENT_DEFAULT_SMS, message)), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(event.put(RESULT, new JsonArray().addAll(records)), policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }

            }

            if (notificationContext.containsKey(Notification.NotificationType.SOUND.getName())
                    && !notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).isEmpty()
                    && notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).containsKey(severity))
            {
                Notification.playSound(new JsonObject().mergeIn(context));
            }

            if (notificationContext.containsKey(CHANNELS)
                    && notificationContext.getJsonObject(CHANNELS) != null
                    && !notificationContext.getJsonObject(CHANNELS).isEmpty()
                    && notificationContext.getJsonObject(CHANNELS).containsKey(severity)
                    && notificationContext.getJsonObject(CHANNELS).getJsonArray(severity) != null
                    && !notificationContext.getJsonObject(CHANNELS).getJsonArray(severity).isEmpty())
            {

                if (policy.containsKey(POLICY_MESSAGE) && !policy.getString(POLICY_MESSAGE).isEmpty())
                {
                    context.put(POLICY_MESSAGE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? PolicyEngineConstants.replaceTrapPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message, event) : PolicyEngineConstants.replaceEventPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE), message));
                }
                else
                {
                    context.put(POLICY_MESSAGE, message);
                }

                var recipients = notificationContext.getJsonObject(CHANNELS).getJsonArray(severity);

                var channelRecipients = new HashMap<String, Set<String>>();

                for (var index = 0; index < recipients.size(); index++)
                {
                    var recipient = recipients.getJsonObject(index);

                    var item = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                    if (item != null)
                    {
                        // Groups the handle list based on the integration type and forwards it to the corresponding notification type.
                        channelRecipients.computeIfAbsent(item.getString(Integration.INTEGRATION_TYPE), key -> new HashSet<>())
                                .add(recipient.getString(ID));
                    }
                }

                channelRecipients.forEach((channelType, targets) ->
                {
                    if (!targets.isEmpty())
                    {
                        var triggerCondition = EMPTY_VALUE;

                        if (!policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()) && !severity.equalsIgnoreCase(Severity.CLEAR.name()))
                        {
                            triggerCondition = String.format("metric value (%s)", PolicyEngineConstants.toOperatorLiteral(policy.getJsonObject(POLICY_CONTEXT).getString(OPERATOR)).toLowerCase());
                        }

                        Notification.send(new JsonObject()
                                .mergeIn(context)
                                .put(NOTIFICATION_TYPE, channelType)
                                .put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(policy.mergeIn(context)
                                        .put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                        .put(EVENT_SOURCE, event.getString(EVENT_SOURCE, EMPTY_VALUE))
                                        .put("trigger.condition", triggerCondition).getMap())
                                        .replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, false))));
                    }
                });
            }
        }


        if (actions != null && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
        {
            var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

            for (var index = 0; index < entries.size(); index++)
            {
                vertx.eventBus().send(EventBusConstants.EVENT_RUNBOOK, getContext(entries.getJsonObject(index).getLong(ID), event, policy));
            }
        }
    }

    /**
     * Creates a context object for runbook execution.
     * <p>
     * This method prepares the context needed for executing a runbook
     * in response to a triggered policy.
     *
     * @param id      The ID of the runbook
     * @param context The original context
     * @param policy  The policy configuration
     * @return A JsonObject containing the runbook execution context
     */
    private JsonObject getContext(long id, JsonObject context, JsonObject policy)
    {
        var runbookContext = new JsonObject().mergeIn(context).put(ID, CommonUtil.getLong(id))
//                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(SYSTEM_REMOTE_ADDRESS))
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(POLICY_ID, policy.getLong(ID))
                .put(POLICY_NAME, policy.getString(POLICY_NAME))
                .put(User.USER_NAME, DEFAULT_USER);

        // MOTADATA-4481
        if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS))
        {
            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS);

            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT);
        }

        return runbookContext;
    }

    /**
     * Records information about a triggered policy action.
     * <p>
     * This method writes information about a triggered policy action to the datastore,
     * including the result, status, and any error information.
     *
     * @param event    The event that triggered the policy
     * @param policyId The ID of the policy
     * @param context  The context information for the triggered action
     */
    private void writeTriggeredActionEvent(JsonObject event, long policyId, JsonObject context)
    {
        var value = EMPTY_VALUE;

        try
        {

            if (PolicyTriggerActionType.valueOfName(context.getString(RUNBOOK_WORKLOG_TYPE)) == PolicyTriggerActionType.RUNBOOK)
            {
                var result = context.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !context.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty() ? context.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0) : null;

                if (result != null && !result.isEmpty())
                {
                    if (result.containsKey(RESULT))
                    {
                        value = CommonUtil.getString(result.getValue(RESULT));
                    }

                    context.put(STATUS, result.getValue(STATUS));
                }

                context.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.RUNBOOK.ordinal());
            }
            else
            {
                value = CommonUtil.getString(context.getJsonArray("recipients"));

                context.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.ordinal());
            }

            var error = context.getString(ERROR);

            DatastoreConstants.write(new JsonObject().put(RUNBOOK_WORKLOG_STATUS, context.getValue(STATUS, STATUS_FAIL))
                            .put(RUNBOOK_WORKLOG_RESULT, value.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? value.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : value)
                            .put(RUNBOOK_WORKLOG_ID, context.containsKey(ID) ? context.getLong(ID) : 0)
                            .put(RUNBOOK_WORKLOG_TYPE, context.getValue(RUNBOOK_WORKLOG_TYPE))
                            .put(POLICY_ID, policyId)
                            .put(AIOpsObject.OBJECT_ID, 0).put(NetRoute.NETROUTE_ID, 0)
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                            .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                            .put(USER_NAME, DEFAULT_USER)
                            .put(RUNBOOK_WORKLOG_ERROR, error != null ? error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : error : EMPTY_VALUE)
                            .put(EVENT_SOURCE, EVENT_TRAP.equalsIgnoreCase(event.getString(EVENT)) ? event.getString(EVENT_SOURCE) : LOCAL_HOST),
                    VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enriches a policy configuration with additional information.
     * <p>
     * This method processes the policy configuration to ensure it contains
     * all necessary information for evaluation, such as converting range values
     * to the appropriate format.
     *
     * @param policy The policy configuration to enrich
     * @return The enriched policy configuration
     */
    private JsonObject enrich(JsonObject policy)
    {
        try
        {
            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
            {
                var values = context.getString(VALUE).split(HASH_SEPARATOR);

                context.put(VALUE, new JsonArray().add(values[0]).add(values[1]));
            }

            // Pre-cache the field key for this policy if it has dataPoint and aggregator
            if (context.containsKey(VisualizationConstants.DATA_POINT) &&
                    context.containsKey(VisualizationConstants.AGGREGATOR))
            {
                getFieldKey(context.getString(VisualizationConstants.DATA_POINT),
                        context.getString(VisualizationConstants.AGGREGATOR));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return policy;
    }

    /**
     * Gets a cached field key or creates and caches a new one.
     * <p>
     * This method creates a field key from a data point and aggregator,
     * caching the result to avoid repeated string concatenations.
     *
     * @param dataPoint  The data point name
     * @param aggregator The aggregator name
     * @return The field key
     */
    private String getFieldKey(String dataPoint, String aggregator)
    {
        return cacheFields.computeIfAbsent(dataPoint + "#" + aggregator, value -> dataPoint + CARET_SEPARATOR + aggregator);
    }

    /**
     * Registers a policy for periodic evaluation.
     * Calculates the interval in seconds based on the evaluation window unit.
     * Also initializes group-by counters if applicable.
     *
     * @param policy The policy to evaluate
     */
    private void add(JsonObject policy)
    {
        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (YES.equalsIgnoreCase(policy.getString(POLICY_STATE)) &&
                (!policy.containsKey(POLICY_SCHEDULED) || policy.getString(POLICY_SCHEDULED).equalsIgnoreCase(NO)) &&
                !evaluatedPolicies.containsKey(policy.getLong(ID)) && !PolicyType.TRAP.getName().equalsIgnoreCase(policy.getString(POLICY_TYPE)))
        {
            var interval = 0;

            switch (policyContext.getString(EVALUATION_WINDOW_UNIT))
            {
                case DateTimeUtil.SECOND -> interval = policyContext.getInteger(EVALUATION_WINDOW);
                case DateTimeUtil.MINUTE -> interval = policyContext.getInteger(EVALUATION_WINDOW) * 60;
                case DateTimeUtil.HOUR -> interval = policyContext.getInteger(EVALUATION_WINDOW) * 3600;
                case DateTimeUtil.DAY -> interval = policyContext.getInteger(EVALUATION_WINDOW) * 86400;
                default -> interval = 300; // fallback
            }

            // Store as [current countdown, original interval]
            evaluatedPolicies.put(policy.getLong(ID), new int[]{interval, interval});

            policyEvaluationTicks.put(policy.getLong(ID), DateTimeUtil.currentSeconds());
        }

        var groupByFields = policyContext.getJsonArray(POLICY_RESULT_BY);

        groupingCounters.put(policy.getLong(ID), new String[4]);

        if (groupByFields != null && !groupByFields.isEmpty())
        {
            for (var i = 0; i < groupByFields.size(); i++)
            {
                groupingCounters.get(policy.getLong(ID))[i] = groupByFields.getString(i);
            }
        }
    }

    /**
     * Sorts the entries in the given JsonObject based on the specified aggregation type (sum, avg, count).
     * Returns the top N entries as defined by the size parameter.
     *
     * @param groupStats The input statistics map to sort
     * @param aggregator The aggregation function to use for sorting
     * @return List of sorted entries based on aggregation values
     */
    private List<Map.Entry<String, Object>> sort(JsonObject groupStats, String aggregator)
    {
        var stats = new ArrayList<>(groupStats.getMap().entrySet());

        stats.sort((stats1, stats2) ->
        {
            var values1 = (JsonArray) stats1.getValue();

            var values2 = (JsonArray) stats2.getValue();

            long value1, value2;

            if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()))
            {
                value1 = values1.getLong(1);

                value2 = values2.getLong(1);
            }
            else if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
            {
                // Handle division by zero - if count is 0, avg is 0

                value1 = values1.getLong(0) > 0 ? (values1.getLong(1) / values1.getLong(0)) : 0;

                value2 = values2.getLong(0) > 0 ? (values2.getLong(1) / values2.getLong(0)) : 0;
            }
            else
            {
                value1 = values1.getLong(0);

                value2 = values2.getLong(0);
            }

            return Long.compare(value2, value1); // Descending order
        });

        return EventPolicyInspector.TOP_N_GROUPS > 0 ? stats.subList(0, Math.min(EventPolicyInspector.TOP_N_GROUPS, stats.size())) : stats;
    }
}
