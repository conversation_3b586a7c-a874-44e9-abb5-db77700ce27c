{"entity": "Widget", "table": "tbl_config_widget", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "visualization.name", "title": "Visualization Name", "type": "String", "rules": ["required", "unique"]}, {"name": "visualization.timeline", "title": "Visualization Timeline", "type": "map", "rules": ["required"]}, {"name": "visualization.category", "title": "Visualization Category", "type": "String", "rules": ["required"]}, {"name": "visualization.type", "title": "Visualization Type", "type": "String"}, {"name": "group.context", "title": "Group Context", "type": "map"}], "entries": [{"type": "directory", "encoded": "no", "records": ["template-widgets", "dashboard-widgets"], "version": "11.0"}]}