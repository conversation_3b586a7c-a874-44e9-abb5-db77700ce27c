{"entity": "User Role", "table": "tbl_config_user_role", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "user.role.name", "title": "Role Name", "type": "string", "rules": ["required", "unique"]}, {"name": "user.role.context", "title": "User Role", "type": "list", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"user.role.name": "admin", "user.role.context": ["user-settings:read", "user-settings:read-write", "user-settings:delete", "system-settings:read", "system-settings:read-write", "system-settings:delete", "discovery-settings:read", "discovery-settings:read-write", "discovery-settings:delete", "monitor-settings:read", "monitor-settings:read-write", "monitor-settings:delete", "group-settings:read", "group-settings:read-write", "group-settings:delete", "agent-settings:read", "agent-settings:read-write", "agent-settings:delete", "snmp-trap-settings:read", "snmp-trap-settings:read-write", "snmp-trap-settings:delete", "plugin-library-settings:read", "plugin-library-settings:read-write", "plugin-library-settings:delete", "audit-settings:read", "my-account-settings:read", "my-account-settings:read-write", "notification-settings:read", "dashboards:read-write", "dashboards:delete", "dashboards:read", "inventory:read-write", "inventory:delete", "inventory:read", "templates:read-write", "templates:delete", "templates:read", "widgets:read-write", "widgets:delete", "widgets:read", "policy-settings:read-write", "policy-settings:delete", "policy-settings:read", "flow-settings:read", "flow-settings:read-write", "flow-settings:delete", "log-settings:read", "log-settings:read-write", "log-settings:delete", "aiops-settings:read", "aiops-settings:read-write", "aiops-settings:delete", "log-explorer:read", "flow-explorer:read", "alert-explorer:read", "trap-explorer:read", "topology:read", "topology:read-write", "topology:delete", "reports:read", "reports:read-write", "reports:delete", "config:read", "config:read-write", "config:delete", "alert-explorer:read-write", "integrations:read", "integrations:read-write", "integrations:delete", "compliance-settings:read-write", "compliance-settings:delete", "compliance-settings:read", "tag-rules:read", "tag-rules:read-write", "tag-rules:delete", "netroute-settings:read", "netroute-settings:read-write", "netroute-settings:delete", "netroute-explorer:read", "metric-explorers:read", "metric-explorers:read-write", "metric-explorers:delete", "netroute-explorer:read", "query:read", "query:read-write", "health-monitoring:read", "health-monitoring:read-write", "dns-server-settings:read", "dns-server-settings:read-write", "dns-server-settings:delete"], "id": **************}, {"user.role.name": "read-only", "user.role.context": ["user-settings:read", "my-account-settings:read-write", "system-settings:read", "discovery-settings:read", "monitor-settings:read", "group-settings:read", "agent-settings:read", "snmp-trap-settings:read", "plugin-library-settings:read", "audit-settings:read", "my-account-settings:read", "notification-settings:read", "dashboards:read", "inventory:read", "templates:read", "widgets:read", "policy-settings:read", "flow-settings:read", "log-settings:read", "aiops-settings:read", "metric-explorer:read", "log-explorer:read", "flow-explorer:read", "alert-explorer:read", "trap-explorer:read", "topology:read", "reports:read", "config:read", "netroute-settings:read", "netroute-explorer:read", "integrations:read", "compliance-settings:read", "tag-rules:read", "metric-explorers:read", "health-monitoring:read", "dns-server-settings:read"], "id": **************}], "version": "2.6"}]}