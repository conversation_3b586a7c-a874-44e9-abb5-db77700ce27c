{"entity": "Scheduler", "table": "tbl_config_scheduler", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "scheduler.start.date", "title": "Scheduler Start Date", "type": "string", "rules": ["required"]}, {"name": "scheduler.times", "title": "Scheduler Time(s)", "values": ["00:00", "00:05", "00:10", "00:15", "00:20", "00:25", "00:30", "00:35", "00:40", "00:45", "00:50", "00:55", "01:00", "01:05", "01:10", "01:15", "01:20", "01:25", "01:30", "01:35", "01:40", "01:45", "01:50", "01:55", "02:00", "02:05", "02:10", "02:15", "02:20", "02:25", "02:30", "02:35", "02:40", "02:45", "02:50", "02:55", "03:00", "03:05", "03:10", "03:15", "03:20", "03:25", "03:30", "03:35", "03:40", "03:45", "03:50", "03:55", "04:00", "04:05", "04:10", "04:15", "04:20", "04:25", "04:30", "04:35", "04:40", "04:45", "04:50", "04:55", "05:00", "05:05", "05:10", "05:15", "05:20", "05:25", "05:30", "05:35", "05:40", "05:45", "05:50", "05:55", "06:00", "06:05", "06:10", "06:15", "06:20", "06:25", "06:30", "06:35", "06:40", "06:45", "06:50", "06:55", "07:00", "07:05", "07:10", "07:15", "07:20", "07:25", "07:30", "07:35", "07:40", "07:45", "07:50", "07:55", "08:00", "08:05", "08:10", "08:15", "08:20", "08:25", "08:30", "08:35", "08:40", "08:45", "08:50", "08:55", "09:00", "09:05", "09:10", "09:15", "09:20", "09:25", "09:30", "09:35", "09:40", "09:45", "09:50", "09:55", "10:00", "10:05", "10:10", "10:15", "10:20", "10:25", "10:30", "10:35", "10:40", "10:45", "10:50", "10:55", "11:00", "11:05", "11:10", "11:15", "11:20", "11:25", "11:30", "11:35", "11:40", "11:45", "11:50", "11:55", "12:00", "12:05", "12:10", "12:15", "12:20", "12:25", "12:30", "12:35", "12:40", "12:45", "12:50", "12:55", "13:00", "13:05", "13:10", "13:15", "13:20", "13:25", "13:30", "13:35", "13:40", "13:45", "13:50", "13:55", "14:00", "14:05", "14:10", "14:15", "14:20", "14:25", "14:30", "14:35", "14:40", "14:45", "14:50", "14:55", "15:00", "15:05", "15:10", "15:15", "15:20", "15:25", "15:30", "15:35", "15:40", "15:45", "15:50", "15:55", "16:00", "16:05", "16:10", "16:15", "16:20", "16:25", "16:30", "16:35", "16:40", "16:45", "16:50", "16:55", "17:00", "17:05", "17:10", "17:15", "17:20", "17:25", "17:30", "17:35", "17:40", "17:45", "17:50", "17:55", "18:00", "18:05", "18:10", "18:15", "18:20", "18:25", "18:30", "18:35", "18:40", "18:45", "18:50", "18:55", "19:00", "19:05", "19:10", "19:15", "19:20", "19:25", "19:30", "19:35", "19:40", "19:45", "19:50", "19:55", "20:00", "20:05", "20:10", "20:15", "20:20", "20:25", "20:30", "20:35", "20:40", "20:45", "20:50", "20:55", "21:00", "21:05", "21:10", "21:15", "21:20", "21:25", "21:30", "21:35", "21:40", "21:45", "21:50", "21:55", "22:00", "22:05", "22:10", "22:15", "22:20", "22:25", "22:30", "22:35", "22:40", "22:45", "22:50", "22:55", "23:00", "23:05", "23:10", "23:15", "23:20", "23:25", "23:30", "23:35", "23:40", "23:45", "23:50", "23:55"], "type": "list", "rules": ["required"]}, {"name": "scheduler.timeline", "title": "Scheduler Timeline", "type": "string", "values": ["Once", "Daily", "Weekly", "Monthly"], "rules": ["required"]}, {"name": "scheduler.days", "title": "Select Day(s)", "values": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "list", "rules": ["required"], "prerequisites": [{"rule": "scheduler.timeline", "value": "Weekly"}]}, {"name": "scheduler.months", "title": "Select Month(s)", "values": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "type": "list", "rules": ["required"], "prerequisites": [{"rule": "scheduler.timeline", "value": "Monthly"}]}, {"name": "scheduler.dates", "title": "Select Date(s)", "values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30"], "type": "list", "rules": ["required"], "prerequisites": [{"rule": "scheduler.timeline", "value": "Monthly"}]}, {"name": "scheduler.job.type", "title": "Job Type", "type": "string", "rules": ["required"], "values": ["Discovery Profile", "Runbook", "Topology", "Rediscover", "Maintenance", "Runbook", "Config Backup Ops", "Topology", "Database Backup"]}, {"name": "scheduler.email.recipients", "title": "Scheduler <PERSON><PERSON>(s)", "type": "list"}, {"name": "scheduler.sms.recipients", "title": "Scheduler SMS Recipient(s)", "type": "list"}], "entries": [{"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Network Interface", "auto.provision.status": "yes"}, "id": 10000000000001}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:30"], "scheduler.days": ["monday"], "scheduler.timeline": "Weekly", "scheduler.context": {"rediscover.job": "Application"}, "id": 10000000000002}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:45"], "scheduler.days": ["monday"], "scheduler.timeline": "Weekly", "scheduler.context": {"rediscover.job": "Cloud", "auto.provision.status": "yes"}, "id": 10000000000003}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["01:00"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Virtual Machine", "auto.provision.status": "yes"}, "id": 10000000000004}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["01:30"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "File/Directory", "auto.provision.status": "yes"}, "id": 10000000000005}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["01:45"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Process", "auto.provision.status": "no"}, "id": 10000000000006}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["02:00"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Windows Service", "auto.provision.status": "yes"}, "id": 10000000000007}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["02:15"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Network Service", "auto.provision.status": "yes"}, "id": 10000000000008}, {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["02:30"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Access Point", "auto.provision.status": "yes"}, "id": 10000000000009}, {"scheduler.state": "yes", "scheduler.job.type": "Config DB Compact", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:40", "01:40", "02:40", "03:40", "04:40", "05:40", "06:40", "07:40", "08:40", "09:40", "10:40", "11:40", "12:40", "13:40", "14:40", "15:40", "16:40", "17:40", "18:40", "19:40", "20:40", "21:40", "22:40", "23:40"], "scheduler.timeline": "Daily", "scheduler.context": {}, "id": 10000000000010}, {"scheduler.state": "yes", "scheduler.job.type": "System Log Retention", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {}, "id": 10000000000011}, {"scheduler.state": "yes", "scheduler.job.type": "Topology", "scheduler.start.date": "01-10-2021", "scheduler.times": ["12:00"], "scheduler.timeline": "Daily", "scheduler.context": {"topology.plugin.type": "SNMP", "topology.link.layer": ["L2", "L3"]}, "id": 10000000000012}, {"scheduler.state": "yes", "scheduler.job.type": "License Quota Reset", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {}, "id": 10000000000013}, {"scheduler.state": "yes", "scheduler.job.type": "Database Backup", "scheduler.start.date": "28-10-2023", "scheduler.times": ["12:00"], "scheduler.timeline": "Daily", "scheduler.context": {"objects": [10000000000001]}, "id": 10000000000014}, {"scheduler.state": "no", "scheduler.job.type": "Database Backup", "scheduler.start.date": "28-10-2023", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {"objects": [10000000000002]}, "id": 10000000000015}], "version": "1.1"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Topology", "scheduler.start.date": "01-10-2021", "scheduler.times": ["12:00"], "scheduler.timeline": "Daily", "scheduler.context": {"topology.plugin.type": "SNMP", "topology.link.layer": ["L2"], "topology.protocols": ["CDP", "LLDP"]}, "id": 10000000000012}], "version": "1.2"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "11-09-2024", "scheduler.times": ["02:45"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Virtual Machine (HCI)", "auto.provision.status": "yes"}, "id": 10000000000016}], "version": "1.3"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Compliance Policy", "scheduler.start.date": "11-09-2024", "scheduler.times": ["23:00"], "scheduler.timeline": "Daily", "scheduler.context": {"compliance.policy.job": "motadatadb"}, "id": 10000000000017}], "version": "1.6"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "11-09-2024", "scheduler.times": ["03:00"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Container"}, "id": 10000000000018}], "version": "1.7"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Config DB Compact", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {}, "id": 10000000000010}], "version": "1.8"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "Config Backup Ops", "scheduler.start.date": "01-10-2021", "scheduler.times": ["01:00"], "scheduler.timeline": "Daily", "scheduler.context": {"objects": []}, "_type": "0", "id": 10000000000019}], "version": "1.9"}, {"type": "inline", "records": [{"scheduler.state": "yes", "scheduler.job.type": "DNS Server Resolver", "scheduler.start.date": "01-10-2021", "scheduler.times": ["12:00"], "scheduler.timeline": "Daily", "scheduler.context": {"objects": []}, "_type": "0", "id": 10000000000020}], "version": "2.1"}]}